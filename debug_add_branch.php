<?php
// ملف تشخيص مشكلة إضافة المركز
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>تشخيص مشكلة إضافة المركز</h2>";

try {
    // تضمين ملفات النظام
    echo "<p>1. تحميل ملفات النظام...</p>";
    require_once 'config/database.php';
    require_once 'includes/functions.php';
    
    echo "<p style='color: green;'>✅ تم تحميل الملفات بنجاح</p>";
    
    // اختبار الاتصال
    echo "<p>2. اختبار الاتصال بقاعدة البيانات...</p>";
    $pdo = getConnection();
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات ناجح</p>";
    
    // فحص الجداول
    echo "<p>3. فحص وجود جدول branches...</p>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'branches'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        echo "<p style='color: green;'>✅ جدول branches موجود</p>";
        
        // عرض هيكل الجدول
        echo "<p>4. فحص هيكل جدول branches...</p>";
        $stmt = $pdo->query("DESCRIBE branches");
        $columns = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>العمود</th><th>النوع</th><th>NULL</th><th>المفتاح</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } else {
        echo "<p style='color: red;'>❌ جدول branches غير موجود</p>";
        echo "<p><a href='setup_database.php'>إنشاء الجداول</a></p>";
        exit;
    }
    
    // اختبار دالة توليد الكود
    echo "<p>5. اختبار دالة توليد الكود...</p>";
    
    // نسخ دالة توليد الكود من add_branch.php
    function generateBranchCode($name) {
        // استخراج الأحرف الأولى من اسم الفرع
        $words = explode(' ', trim($name));
        $prefix = '';
        
        foreach ($words as $word) {
            if (!empty($word)) {
                // تحويل الأحرف العربية إلى إنجليزية
                $arabicToEnglish = [
                    'ا' => 'A', 'أ' => 'A', 'إ' => 'A', 'آ' => 'A',
                    'ب' => 'B', 'ت' => 'T', 'ث' => 'TH', 'ج' => 'J',
                    'ح' => 'H', 'خ' => 'KH', 'د' => 'D', 'ذ' => 'DH',
                    'ر' => 'R', 'ز' => 'Z', 'س' => 'S', 'ش' => 'SH',
                    'ص' => 'S', 'ض' => 'D', 'ط' => 'T', 'ظ' => 'Z',
                    'ع' => 'A', 'غ' => 'GH', 'ف' => 'F', 'ق' => 'Q',
                    'ك' => 'K', 'ل' => 'L', 'م' => 'M', 'ن' => 'N',
                    'ه' => 'H', 'و' => 'W', 'ي' => 'Y', 'ى' => 'Y',
                    'ة' => 'H'
                ];
                
                $firstChar = mb_substr($word, 0, 1);
                if (isset($arabicToEnglish[$firstChar])) {
                    $prefix .= $arabicToEnglish[$firstChar];
                } else {
                    $prefix .= strtoupper($firstChar);
                }
            }
        }
        
        // التأكد من أن الكود لا يقل عن 3 أحرف
        if (strlen($prefix) < 3) {
            $prefix = strtoupper(substr(md5($name), 0, 3));
        }
        
        // قطع الكود إلى 3-4 أحرف كحد أقصى
        $prefix = substr($prefix, 0, 4);
        
        // البحث عن رقم تسلسلي متاح
        $counter = 1;
        do {
            $code = $prefix . str_pad($counter, 3, '0', STR_PAD_LEFT);
            $stmt = executeQuery("SELECT id FROM branches WHERE code = ?", [$code]);
            $exists = $stmt->fetch();
            $counter++;
        } while ($exists && $counter <= 999);
        
        return $code;
    }
    
    $testName = "مركز عمان المركزي";
    $generatedCode = generateBranchCode($testName);
    echo "<p style='color: green;'>✅ تم توليد الكود: <strong>$generatedCode</strong> للاسم: <strong>$testName</strong></p>";
    
    // اختبار الإدراج
    echo "<p>6. اختبار إدراج مركز تجريبي...</p>";
    
    $testData = [
        'name' => 'مركز اختبار تشخيصي',
        'code' => generateBranchCode('مركز اختبار تشخيصي'),
        'address' => 'عنوان تجريبي',
        'phone' => '123456789',
        'manager_name' => 'مدير تجريبي'
    ];
    
    echo "<p>البيانات التجريبية:</p>";
    echo "<ul>";
    foreach ($testData as $key => $value) {
        echo "<li><strong>$key:</strong> $value</li>";
    }
    echo "</ul>";
    
    $sql = "INSERT INTO branches (name, code, address, phone, manager_name, created_at) 
            VALUES (?, ?, ?, ?, ?, NOW())";
    
    echo "<p>الاستعلام: <code>$sql</code></p>";
    
    $stmt = executeQuery($sql, [
        $testData['name'],
        $testData['code'],
        $testData['address'],
        $testData['phone'],
        $testData['manager_name']
    ]);
    
    $insertId = getLastInsertId();
    echo "<p style='color: green;'>✅ تم إدراج المركز بنجاح! ID: $insertId</p>";
    
    // حذف البيانات التجريبية
    executeQuery("DELETE FROM branches WHERE id = ?", [$insertId]);
    echo "<p style='color: blue;'>🗑️ تم حذف البيانات التجريبية</p>";
    
    echo "<h3 style='color: green;'>🎉 جميع الاختبارات نجحت!</h3>";
    echo "<p>المشكلة قد تكون في:</p>";
    echo "<ul>";
    echo "<li>بيانات الإدخال في النموذج</li>";
    echo "<li>الجلسة (Session)</li>";
    echo "<li>دالة logActivity</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>تفاصيل الخطأ:</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة إضافة المركز</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background-color: #f2f2f2; }
        code { background: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
        pre { background: #f8f8f8; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <p><a href="add_branch.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">العودة لإضافة مركز</a></p>
</body>
</html>
