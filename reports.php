<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

checkSession();
$user = getUserById($_SESSION['user_id']);

// جلب قائمة الفروع للفلترة
$branches = fetchAll("SELECT id, name, code FROM branches WHERE is_active = 1 ORDER BY name");

// معالجة الفلاتر
$branchFilter = isset($_GET['branch_id']) && !empty($_GET['branch_id']) ? $_GET['branch_id'] : null;
$dateFrom = isset($_GET['date_from']) && !empty($_GET['date_from']) ? $_GET['date_from'] : date('Y-m-01');
$dateTo = isset($_GET['date_to']) && !empty($_GET['date_to']) ? $_GET['date_to'] : date('Y-m-d');
$reportType = isset($_GET['report_type']) ? $_GET['report_type'] : 'summary';

// بناء شروط الاستعلام
$whereConditions = ["1=1"];
$params = [];

if ($branchFilter) {
    $whereConditions[] = "b.id = ?";
    $params[] = $branchFilter;
}

$whereConditions[] = "b.created_at BETWEEN ? AND ?";
$params[] = $dateFrom . ' 00:00:00';
$params[] = $dateTo . ' 23:59:59';

$whereClause = implode(' AND ', $whereConditions);

// جلب البيانات حسب نوع التقرير
try {
    switch ($reportType) {
        case 'summary':
            // تقرير ملخص الفروع
            $reportData = fetchAll("
                SELECT 
                    b.id,
                    b.name,
                    b.code,
                    b.address,
                    b.phone,
                    b.manager_name,
                    b.is_active,
                    b.created_at,
                    COUNT(DISTINCT u.id) as total_users,
                    COUNT(DISTINCT CASE WHEN u.is_active = 1 THEN u.id END) as active_users,
                    COUNT(DISTINCT p.id) as total_pos,
                    COUNT(DISTINCT CASE WHEN p.is_active = 1 THEN p.id END) as active_pos
                FROM branches b
                LEFT JOIN users u ON b.id = u.branch_id
                LEFT JOIN point_of_sales p ON b.id = p.branch_id
                WHERE $whereClause
                GROUP BY b.id
                ORDER BY b.name
            ", $params);
            break;
            
        case 'users':
            // تقرير المستخدمين
            $reportData = fetchAll("
                SELECT 
                    u.id,
                    u.username,
                    u.full_name,
                    u.email,
                    u.phone,
                    u.user_type,
                    u.department,
                    u.is_active,
                    u.last_login,
                    u.created_at,
                    b.name as branch_name,
                    b.code as branch_code
                FROM users u
                LEFT JOIN branches b ON u.branch_id = b.id
                WHERE b.id IS NOT NULL AND $whereClause
                ORDER BY b.name, u.full_name
            ", $params);
            break;
            
        case 'pos':
            // تقرير نقاط البيع
            $reportData = fetchAll("
                SELECT 
                    p.id,
                    p.name,
                    p.code,
                    p.type,
                    p.location,
                    p.manager_name,
                    p.is_active,
                    p.created_at,
                    b.name as branch_name,
                    b.code as branch_code
                FROM point_of_sales p
                INNER JOIN branches b ON p.branch_id = b.id
                WHERE $whereClause
                ORDER BY b.name, p.name
            ", $params);
            break;
            
        default:
            $reportData = [];
    }
} catch (Exception $e) {
    $reportData = [];
    $error = "خطأ في جلب البيانات: " . $e->getMessage();
}

// أنواع المستخدمين
$userTypes = [
    'admin' => 'مدير النظام',
    'main_accountant' => 'محاسب رئيسي',
    'branch_manager' => 'مدير فرع',
    'financial_manager' => 'رئيس فرع مالي',
    'department_head' => 'رئيس قسم',
    'accountant' => 'محاسب',
    'cashier' => 'أمين صندوق',
    'employee' => 'موظف'
];

// أنواع نقاط البيع
$posTypes = [
    'supermarket' => 'سوبر ماركت',
    'cafeteria' => 'كافتيريا',
    'nuts_shop' => 'محل مكسرات',
    'sweets' => 'حلويات',
    'bakery' => 'مخبز'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير - نظام المحاسبة المالية</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body { 
            font-family: 'Cairo', sans-serif; 
            background-color: #f8f9fa;
        }
        
        .report-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
        }
        
        .filter-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .report-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .table-responsive {
            border-radius: 8px;
            overflow: hidden;
        }
        
        .report-summary {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .export-buttons {
            margin-bottom: 20px;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            .report-card {
                box-shadow: none;
                border: 1px solid #ddd;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>
    
    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Main Content Area -->
            <main class="col-12 px-md-4 main-content-with-right-sidebar">
                <!-- Report Header -->
                <div class="report-header">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="mb-2">
                                <i class="fas fa-chart-line me-3"></i>
                                التقارير والإحصائيات
                            </h1>
                            <p class="mb-0">تقارير شاملة عن المراكز والمستخدمين ونقاط البيع</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="btn-group no-print">
                                <button type="button" class="btn btn-light" onclick="window.print()">
                                    <i class="fas fa-print me-2"></i>
                                    طباعة
                                </button>
                                <button type="button" class="btn btn-light" onclick="exportToExcel()">
                                    <i class="fas fa-file-excel me-2"></i>
                                    تصدير Excel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="filter-card no-print">
                    <h5 class="mb-3">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر التقرير
                    </h5>
                    
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="report_type" class="form-label">نوع التقرير</label>
                            <select class="form-select" id="report_type" name="report_type">
                                <option value="summary" <?php echo $reportType == 'summary' ? 'selected' : ''; ?>>ملخص المراكز</option>
                                <option value="users" <?php echo $reportType == 'users' ? 'selected' : ''; ?>>تقرير المستخدمين</option>
                                <option value="pos" <?php echo $reportType == 'pos' ? 'selected' : ''; ?>>تقرير نقاط البيع</option>
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="branch_id" class="form-label">المركز</label>
                            <select class="form-select" id="branch_id" name="branch_id">
                                <option value="">جميع المراكز</option>
                                <?php foreach ($branches as $branch): ?>
                                <option value="<?php echo $branch['id']; ?>" <?php echo $branchFilter == $branch['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($branch['name']) . ' (' . htmlspecialchars($branch['code']) . ')'; ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $dateFrom; ?>">
                        </div>
                        
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $dateTo; ?>">
                        </div>
                        
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>
                                    تطبيق الفلاتر
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Report Summary -->
                <?php if (!empty($reportData)): ?>
                <div class="report-summary">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <h4 class="text-primary"><?php echo count($reportData); ?></h4>
                            <p class="mb-0">إجمالي السجلات</p>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-success">
                                <?php 
                                if ($reportType == 'summary') {
                                    echo count(array_filter($reportData, function($item) { return $item['is_active']; }));
                                } elseif ($reportType == 'users') {
                                    echo count(array_filter($reportData, function($item) { return $item['is_active']; }));
                                } else {
                                    echo count(array_filter($reportData, function($item) { return $item['is_active']; }));
                                }
                                ?>
                            </h4>
                            <p class="mb-0">النشطة</p>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-info"><?php echo date('Y-m-d', strtotime($dateFrom)) . ' - ' . date('Y-m-d', strtotime($dateTo)); ?></h4>
                            <p class="mb-0">فترة التقرير</p>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-warning"><?php echo date('Y-m-d H:i'); ?></h4>
                            <p class="mb-0">تاريخ التقرير</p>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Report Content -->
                <div class="report-card">
                    <?php if (isset($error)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                    <?php elseif (empty($reportData)): ?>
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-chart-line fa-4x mb-3"></i>
                        <h4>لا توجد بيانات</h4>
                        <p>لا توجد بيانات متاحة للفترة والفلاتر المحددة</p>
                    </div>
                    <?php else: ?>
                    
                    <?php if ($reportType == 'summary'): ?>
                    <!-- تقرير ملخص المراكز -->
                    <h5 class="mb-3">
                        <i class="fas fa-building me-2"></i>
                        تقرير ملخص المراكز
                    </h5>
                    
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الكود</th>
                                    <th>اسم المركز</th>
                                    <th>رئيس الفرع المالي</th>
                                    <th>المستخدمين</th>
                                    <th>نقاط البيع</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإنشاء</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($reportData as $item): ?>
                                <tr>
                                    <td><code><?php echo htmlspecialchars($item['code']); ?></code></td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($item['name']); ?></strong>
                                        <?php if ($item['address']): ?>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($item['address']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($item['manager_name'] ?: 'غير محدد'); ?></td>
                                    <td>
                                        <span class="badge bg-primary"><?php echo $item['total_users']; ?></span>
                                        <small class="text-success">(<?php echo $item['active_users']; ?> نشط)</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?php echo $item['total_pos']; ?></span>
                                        <small class="text-success">(<?php echo $item['active_pos']; ?> نشط)</small>
                                    </td>
                                    <td>
                                        <?php if ($item['is_active']): ?>
                                        <span class="badge bg-success">نشط</span>
                                        <?php else: ?>
                                        <span class="badge bg-secondary">غير نشط</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo date('Y-m-d', strtotime($item['created_at'])); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <?php elseif ($reportType == 'users'): ?>
                    <!-- تقرير المستخدمين -->
                    <h5 class="mb-3">
                        <i class="fas fa-users me-2"></i>
                        تقرير المستخدمين
                    </h5>
                    
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>اسم المستخدم</th>
                                    <th>الاسم الكامل</th>
                                    <th>النوع</th>
                                    <th>المركز</th>
                                    <th>القسم</th>
                                    <th>الحالة</th>
                                    <th>آخر دخول</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($reportData as $item): ?>
                                <tr>
                                    <td><code><?php echo htmlspecialchars($item['username']); ?></code></td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($item['full_name']); ?></strong>
                                        <?php if ($item['email']): ?>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($item['email']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo match($item['user_type']) {
                                                'admin' => 'danger',
                                                'main_accountant' => 'warning',
                                                'branch_manager' => 'primary',
                                                'financial_manager' => 'info',
                                                'department_head' => 'success',
                                                default => 'secondary'
                                            };
                                        ?>">
                                            <?php echo $userTypes[$item['user_type']] ?? $item['user_type']; ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($item['branch_name'] ?: 'غير محدد'); ?></td>
                                    <td><?php echo htmlspecialchars($item['department'] ?: 'غير محدد'); ?></td>
                                    <td>
                                        <?php if ($item['is_active']): ?>
                                        <span class="badge bg-success">نشط</span>
                                        <?php else: ?>
                                        <span class="badge bg-secondary">غير نشط</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($item['last_login']): ?>
                                        <?php echo date('Y-m-d H:i', strtotime($item['last_login'])); ?>
                                        <?php else: ?>
                                        <span class="text-muted">لم يدخل بعد</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <?php elseif ($reportType == 'pos'): ?>
                    <!-- تقرير نقاط البيع -->
                    <h5 class="mb-3">
                        <i class="fas fa-store me-2"></i>
                        تقرير نقاط البيع
                    </h5>
                    
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الكود</th>
                                    <th>اسم نقطة البيع</th>
                                    <th>النوع</th>
                                    <th>المركز</th>
                                    <th>المدير</th>
                                    <th>الموقع</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإنشاء</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($reportData as $item): ?>
                                <tr>
                                    <td><code><?php echo htmlspecialchars($item['code']); ?></code></td>
                                    <td><strong><?php echo htmlspecialchars($item['name']); ?></strong></td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?php echo $posTypes[$item['type']] ?? $item['type']; ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($item['branch_name']); ?></td>
                                    <td><?php echo htmlspecialchars($item['manager_name'] ?: 'غير محدد'); ?></td>
                                    <td><?php echo htmlspecialchars($item['location'] ?: 'غير محدد'); ?></td>
                                    <td>
                                        <?php if ($item['is_active']): ?>
                                        <span class="badge bg-success">نشط</span>
                                        <?php else: ?>
                                        <span class="badge bg-secondary">غير نشط</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo date('Y-m-d', strtotime($item['created_at'])); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                    
                    <?php endif; ?>
                </div>
            </main>
            
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تصدير إلى Excel
        function exportToExcel() {
            const table = document.querySelector('.table');
            if (!table) {
                alert('لا توجد بيانات للتصدير');
                return;
            }
            
            // إنشاء ملف Excel بسيط
            let csv = '';
            const rows = table.querySelectorAll('tr');
            
            rows.forEach(row => {
                const cols = row.querySelectorAll('th, td');
                const rowData = [];
                cols.forEach(col => {
                    rowData.push('"' + col.textContent.trim().replace(/"/g, '""') + '"');
                });
                csv += rowData.join(',') + '\n';
            });
            
            // تحميل الملف
            const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', 'report_' + new Date().toISOString().slice(0,10) + '.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        
        // تحديث التقرير عند تغيير النوع
        document.getElementById('report_type').addEventListener('change', function() {
            this.form.submit();
        });
    </script>
</body>
</html>
