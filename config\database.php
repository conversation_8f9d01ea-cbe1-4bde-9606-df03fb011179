<?php
// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'md3');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إنشاء اتصال قاعدة البيانات
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    );
} catch (PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// إعدادات النظام
define('SYSTEM_NAME', 'نظام المحاسبة المالية');
define('SYSTEM_VERSION', '1.0.0');
define('CURRENCY', 'دينار أردني');
define('CURRENCY_SYMBOL', 'د.أ');
define('DATE_FORMAT', 'Y-m-d');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');

// إعدادات الأمان
define('SESSION_TIMEOUT', 3600); // ساعة واحدة
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 دقيقة

// إعدادات الملفات
define('UPLOAD_PATH', 'uploads/');
define('BACKUP_PATH', 'backups/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5 ميجابايت

// أنواع المستخدمين
define('USER_TYPE_ADMIN', 'admin');
define('USER_TYPE_MAIN_ACCOUNTANT', 'main_accountant');
define('USER_TYPE_BRANCH_ACCOUNTANT', 'branch_accountant');
define('USER_TYPE_CASHIER', 'cashier');

// حالات الفواتير
define('INVOICE_STATUS_DRAFT', 'draft');
define('INVOICE_STATUS_PENDING', 'pending');
define('INVOICE_STATUS_APPROVED', 'approved');
define('INVOICE_STATUS_PAID', 'paid');
define('INVOICE_STATUS_CANCELLED', 'cancelled');

// أنواع الحركات المالية
define('TRANSACTION_TYPE_SALE', 'sale');
define('TRANSACTION_TYPE_PURCHASE', 'purchase');
define('TRANSACTION_TYPE_EXPENSE', 'expense');
define('TRANSACTION_TYPE_INCOME', 'income');
define('TRANSACTION_TYPE_TRANSFER', 'transfer');

// أنواع نقاط البيع
define('POS_TYPE_SUPERMARKET', 'supermarket');
define('POS_TYPE_CAFETERIA', 'cafeteria');
define('POS_TYPE_NUTS_SHOP', 'nuts_shop');
define('POS_TYPE_SWEETS', 'sweets');
define('POS_TYPE_BAKERY', 'bakery');

// إعدادات التقارير
define('REPORT_ITEMS_PER_PAGE', 50);
define('CHART_COLORS', [
    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
    '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'
]);

// رسائل النظام
define('MSG_SUCCESS', 'success');
define('MSG_ERROR', 'error');
define('MSG_WARNING', 'warning');
define('MSG_INFO', 'info');

// وظائف مساعدة لقاعدة البيانات
function getConnection() {
    global $pdo;
    return $pdo;
}

function executeQuery($sql, $params = []) {
    try {
        $pdo = getConnection();
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch (PDOException $e) {
        error_log("Database Error: " . $e->getMessage());
        // عرض الخطأ الحقيقي للتشخيص
        throw new Exception("خطأ في قاعدة البيانات: " . $e->getMessage());
    }
}

function fetchOne($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt->fetch();
}

function fetchAll($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt->fetchAll();
}

function getLastInsertId() {
    $pdo = getConnection();
    return $pdo->lastInsertId();
}

function beginTransaction() {
    $pdo = getConnection();
    return $pdo->beginTransaction();
}

function commit() {
    $pdo = getConnection();
    return $pdo->commit();
}

function rollback() {
    $pdo = getConnection();
    return $pdo->rollback();
}

// تحقق من وجود قاعدة البيانات وإنشاؤها إذا لم تكن موجودة
function checkAndCreateDatabase() {
    try {
        // محاولة الاتصال بقاعدة البيانات
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET,
            DB_USER,
            DB_PASS
        );

        // إنشاء قاعدة البيانات إذا لم تكن موجودة
        $pdo->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE " . DB_NAME);

        return true;
    } catch (PDOException $e) {
        error_log("Database Creation Error: " . $e->getMessage());
        return false;
    }
}

// استدعاء فحص قاعدة البيانات
checkAndCreateDatabase();
?>
