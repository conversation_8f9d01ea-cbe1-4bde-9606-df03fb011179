<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<h2>اختبار نهائي - الحل البسيط</h2>";

// التحقق من الجلسة
if (!isset($_SESSION['user_id'])) {
    echo "<div style='color: red;'>✗ لم يتم تسجيل الدخول</div>";
    echo "<a href='login.php'>تسجيل الدخول</a>";
    exit();
}

echo "<div style='color: green;'>✓ تم تسجيل الدخول</div>";

// التحقق من المستخدم
$user = getUserById($_SESSION['user_id']);
echo "<p>المستخدم: " . htmlspecialchars($user['full_name']) . " (" . htmlspecialchars($user['user_type']) . ")</p>";

// فحص الملفات الجديدة
echo "<h3>فحص الملفات الجديدة:</h3>";
$files = [
    'branches.php' => 'صفحة الفروع الجديدة',
    'pos.php' => 'صفحة نقاط البيع الجديدة'
];

foreach ($files as $file => $description) {
    if (file_exists($file)) {
        echo "<div style='color: green;'>✓ $file موجود - $description</div>";
    } else {
        echo "<div style='color: red;'>✗ $file غير موجود</div>";
    }
}

// فحص قاعدة البيانات
echo "<h3>فحص قاعدة البيانات:</h3>";
try {
    $branchCount = $pdo->query("SELECT COUNT(*) FROM branches")->fetchColumn();
    echo "<div style='color: green;'>✓ عدد الفروع: $branchCount</div>";
    
    $posCount = $pdo->query("SELECT COUNT(*) FROM point_of_sales")->fetchColumn();
    echo "<div style='color: green;'>✓ عدد نقاط البيع: $posCount</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red;'>✗ خطأ في قاعدة البيانات: " . $e->getMessage() . "</div>";
}

// اختبار الروابط الجديدة
echo "<h3>اختبار الروابط الجديدة:</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<h4>الروابط المباشرة:</h4>";
echo "<p><a href='branches.php' target='_blank' style='color: #007bff;'>صفحة الفروع الجديدة</a></p>";
echo "<p><a href='pos.php' target='_blank' style='color: #007bff;'>صفحة نقاط البيع الجديدة</a></p>";
echo "</div>";

// إضافة بيانات تجريبية إذا لم تكن موجودة
if ($branchCount == 0) {
    echo "<h3>إضافة بيانات تجريبية:</h3>";
    try {
        // إضافة شركة
        $pdo->exec("INSERT IGNORE INTO companies (id, name, address, phone, email) VALUES 
                   (1, 'شركة التجارة المتقدمة', 'عمان - الأردن', '+962-6-1234567', '<EMAIL>')");
        
        // إضافة الفروع
        $pdo->exec("INSERT IGNORE INTO branches (id, company_id, name, code, address, phone, manager_name) VALUES 
                   (1, 1, 'فرع الزرقاء', 'ZRQ001', 'الزرقاء - شارع الملك عبدالله', '+962-5-1234567', 'أحمد محمد'),
                   (2, 1, 'فرع ماركا', 'MRK001', 'ماركا الشمالية - عمان', '+962-6-2345678', 'محمد أحمد'),
                   (3, 1, 'فرع قفقفا', 'QFQ001', 'قفقفا - الزرقاء', '+962-5-3456789', 'سالم علي')");
        
        // إضافة نقاط البيع
        $pdo->exec("INSERT IGNORE INTO point_of_sales (id, branch_id, name, type, description) VALUES 
                   (1, 1, 'سوبر ماركت الزرقاء', 'supermarket', 'سوبر ماركت شامل'),
                   (2, 1, 'كافتيريا الزرقاء', 'cafeteria', 'كافتيريا ومشروبات'),
                   (3, 1, 'محمص الزرقاء', 'nuts_shop', 'بزر ومكسرات'),
                   (4, 1, 'حلويات الزرقاء', 'sweets', 'حلويات شرقية وغربية'),
                   (5, 1, 'مخبز الزرقاء', 'bakery', 'خبز ومعجنات'),
                   (6, 2, 'سوبر ماركت ماركا', 'supermarket', 'سوبر ماركت فرع ماركا'),
                   (7, 2, 'كافتيريا ماركا', 'cafeteria', 'كافتيريا فرع ماركا'),
                   (8, 3, 'سوبر ماركت قفقفا', 'supermarket', 'سوبر ماركت فرع قفقفا')");
        
        echo "<div style='color: green;'>✓ تم إضافة البيانات التجريبية بنجاح</div>";
        
        // إعادة فحص العدد
        $branchCount = $pdo->query("SELECT COUNT(*) FROM branches")->fetchColumn();
        $posCount = $pdo->query("SELECT COUNT(*) FROM point_of_sales")->fetchColumn();
        echo "<div style='color: green;'>✓ عدد الفروع الآن: $branchCount</div>";
        echo "<div style='color: green;'>✓ عدد نقاط البيع الآن: $posCount</div>";
        
    } catch (Exception $e) {
        echo "<div style='color: red;'>✗ خطأ في إضافة البيانات: " . $e->getMessage() . "</div>";
    }
}

echo "<hr>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724;'>";
echo "<h3>✅ الحل النهائي جاهز!</h3>";
echo "<p><strong>الملفات الجديدة:</strong></p>";
echo "<ul>";
echo "<li><strong>branches.php</strong> - صفحة إدارة الفروع (بسيطة ومباشرة)</li>";
echo "<li><strong>pos.php</strong> - صفحة إدارة نقاط البيع (بسيطة ومباشرة)</li>";
echo "</ul>";
echo "<p><strong>كيفية الوصول:</strong></p>";
echo "<ol>";
echo "<li>من الصفحة الرئيسية: قسم \"وصول سريع\"</li>";
echo "<li>من الشريط الجانبي: \"إدارة الفروع\" و \"نقاط البيع\"</li>";
echo "<li>الروابط المباشرة أعلاه</li>";
echo "</ol>";
echo "</div>";

echo "<div style='text-align: center; margin-top: 20px;'>";
echo "<a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>العودة للصفحة الرئيسية</a>";
echo "</div>";
?>

<style>
body { 
    font-family: 'Cairo', sans-serif; 
    margin: 20px; 
    direction: rtl;
    line-height: 1.6;
}
h2, h3 { color: #333; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
