<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

checkSession();
$user = getUserById($_SESSION['user_id']);

// التحقق من الصلاحيات
if (!isAdmin($_SESSION['user_id']) && !isMainAccountant($_SESSION['user_id'])) {
    header('Location: index.php?error=access_denied');
    exit();
}

// جلب نقاط البيع
try {
    $sql = "SELECT p.*, b.name as branch_name, b.code as branch_code
            FROM point_of_sales p
            JOIN branches b ON p.branch_id = b.id
            WHERE p.is_active = 1
            ORDER BY b.name, p.name";

    $pointOfSales = fetchAll($sql);
} catch (Exception $e) {
    $pointOfSales = [];
    $error = 'خطأ في جلب بيانات نقاط البيع: ' . $e->getMessage();
}

// أنواع نقاط البيع
$posTypes = [
    'supermarket' => 'سوبر ماركت',
    'cafeteria' => 'كافتيريا',
    'nuts_shop' => 'محمص',
    'sweets' => 'حلويات',
    'bakery' => 'مخبز'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة نقاط البيع - نظام المحاسبة المالية</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">

    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body { font-family: 'Cairo', sans-serif; }
        .card { margin-bottom: 20px; }
        .table th { background-color: #f8f9fa; }
        .badge { font-size: 0.8em; }
        .btn-group .btn { margin: 0 2px; }
        .pos-card {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        .pos-card:hover {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transform: translateY(-2px);
        }

        /* تخطيط القائمة على اليمين */
        @media (min-width: 768px) {
            .main-content-with-right-sidebar {
                margin-right: 300px;
                padding-right: 20px;
                padding-left: 20px;
            }
        }

        @media (max-width: 767.98px) {
            .main-content-with-right-sidebar {
                margin-right: 0;
                padding-right: 15px;
                padding-left: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Main Content Area -->
            <main class="col-12 px-md-4 main-content-with-right-sidebar">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-cash-register me-2"></i>
                        إدارة نقاط البيع
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="index.php" class="btn btn-outline-secondary">
                                <i class="fas fa-home"></i> الصفحة الرئيسية
                            </a>
                            <a href="branches.php" class="btn btn-outline-primary">
                                <i class="fas fa-store"></i> الفروع
                            </a>
                            <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                        </div>
                    </div>
                </div>

                <?php if (isset($error)): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- إحصائيات نقاط البيع -->
                <div class="row mb-4">
                    <?php
                    $typeStats = [];
                    foreach ($pointOfSales as $pos) {
                        $typeStats[$pos['type']] = ($typeStats[$pos['type']] ?? 0) + 1;
                    }
                    ?>
                    <div class="col-md-2">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h4><?php echo count($pointOfSales); ?></h4>
                                <small>إجمالي نقاط البيع</small>
                            </div>
                        </div>
                    </div>
                    <?php foreach ($posTypes as $type => $typeName): ?>
                    <div class="col-md-2">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h4><?php echo $typeStats[$type] ?? 0; ?></h4>
                                <small><?php echo $typeName; ?></small>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- عرض نقاط البيع كبطاقات -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة نقاط البيع (<?php echo count($pointOfSales); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($pointOfSales)): ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-cash-register fa-3x mb-3"></i>
                            <p>لا توجد نقاط بيع في النظام</p>
                            <?php if (isAdmin($_SESSION['user_id'])): ?>
                            <button class="btn btn-primary" onclick="addPOS()">
                                إضافة نقطة بيع جديدة
                            </button>
                            <?php endif; ?>
                        </div>
                        <?php else: ?>

                        <!-- عرض البطاقات -->
                        <div class="row">
                            <?php foreach ($pointOfSales as $pos): ?>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="pos-card">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="mb-0"><?php echo htmlspecialchars($pos['name']); ?></h6>
                                        <?php
                                        $typeIcons = [
                                            'supermarket' => 'fas fa-shopping-cart text-primary',
                                            'cafeteria' => 'fas fa-coffee text-warning',
                                            'nuts_shop' => 'fas fa-seedling text-success',
                                            'sweets' => 'fas fa-candy-cane text-danger',
                                            'bakery' => 'fas fa-bread-slice text-info'
                                        ];
                                        $icon = $typeIcons[$pos['type']] ?? 'fas fa-store';
                                        ?>
                                        <i class="<?php echo $icon; ?> fa-lg"></i>
                                    </div>

                                    <div class="mb-2">
                                        <span class="badge bg-secondary"><?php echo htmlspecialchars($pos['branch_name']); ?></span>
                                        <small class="text-muted d-block"><?php echo htmlspecialchars($pos['branch_code']); ?></small>
                                    </div>

                                    <div class="mb-2">
                                        <strong>النوع:</strong> <?php echo $posTypes[$pos['type']] ?? $pos['type']; ?>
                                    </div>

                                    <?php if ($pos['description']): ?>
                                    <div class="mb-2">
                                        <small class="text-muted"><?php echo htmlspecialchars($pos['description']); ?></small>
                                    </div>
                                    <?php endif; ?>

                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="badge bg-<?php echo $pos['is_active'] ? 'success' : 'danger'; ?>">
                                            <?php echo $pos['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                        </span>

                                        <?php if (isAdmin($_SESSION['user_id'])): ?>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary"
                                                    onclick="viewPOS(<?php echo $pos['id']; ?>)" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary"
                                                    onclick="editPOS(<?php echo $pos['id']; ?>)" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                    onclick="confirmDeletePOS(<?php echo $pos['id']; ?>, '<?php echo htmlspecialchars($pos['name']); ?>')"
                                                    title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>

                        <!-- عرض الجدول -->
                        <hr>
                        <h6><i class="fas fa-table me-2"></i>عرض الجدول</h6>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>اسم نقطة البيع</th>
                                        <th>الفرع</th>
                                        <th>النوع</th>
                                        <th>الوصف</th>
                                        <th>الحالة</th>
                                        <?php if (isAdmin($_SESSION['user_id'])): ?>
                                        <th>الإجراءات</th>
                                        <?php endif; ?>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($pointOfSales as $pos): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($pos['name']); ?></strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?php echo htmlspecialchars($pos['branch_name']); ?></span>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($pos['branch_code']); ?></small>
                                        </td>
                                        <td>
                                            <?php
                                            $icon = $typeIcons[$pos['type']] ?? 'fas fa-store';
                                            ?>
                                            <i class="<?php echo $icon; ?> me-2"></i>
                                            <?php echo $posTypes[$pos['type']] ?? $pos['type']; ?>
                                        </td>
                                        <td>
                                            <small><?php echo htmlspecialchars($pos['description'] ?? '-'); ?></small>
                                        </td>
                                        <td>
                                            <?php if ($pos['is_active']): ?>
                                            <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                            <span class="badge bg-danger">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <?php if (isAdmin($_SESSION['user_id'])): ?>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary"
                                                        onclick="viewPOS(<?php echo $pos['id']; ?>)" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary"
                                                        onclick="editPOS(<?php echo $pos['id']; ?>)" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger"
                                                        onclick="confirmDeletePOS(<?php echo $pos['id']; ?>, '<?php echo htmlspecialchars($pos['name']); ?>')"
                                                        title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                        <?php endif; ?>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>

            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function confirmDeletePOS(posId, posName) {
            if (confirm('هل أنت متأكد من حذف نقطة البيع "' + posName + '"؟')) {
                window.location.href = '?delete_pos=' + posId;
            }
        }

        function viewPOS(id) {
            alert('عرض تفاصيل نقطة البيع رقم: ' + id);
        }

        function editPOS(id) {
            alert('تعديل نقطة البيع رقم: ' + id);
        }

        function addPOS() {
            alert('إضافة نقطة بيع جديدة');
        }
    </script>
</body>
</html>
