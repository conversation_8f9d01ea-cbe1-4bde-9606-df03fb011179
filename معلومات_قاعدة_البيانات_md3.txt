========================================
    معلومات قاعدة البيانات الجديدة - MD3
========================================

📊 اسم قاعدة البيانات الجديد:
md3

🔐 بيانات الاتصال:
الخادم: localhost
قاعدة البيانات: md3
اسم المستخدم: md
كلمة المرور: mm$$12345dd

========================================

📥 طرق إنشاء/استيراد قاعدة البيانات:

1. **الطريقة الأسهل - معالج التثبيت:**
   http://localhost/MD1/install.php
   
   ✅ سيقوم بإنشاء قاعدة البيانات تلقائياً
   ✅ سيضيف جميع الجداول والبيانات التجريبية
   ✅ البيانات ستكون مملوءة مسبقاً

2. **من phpMyAdmin:**
   - افتح: http://localhost/phpmyadmin
   - سجل الدخول بـ: md / mm$$12345dd
   - أنشئ قاعدة بيانات جديدة: md3
   - استورد الملف: database/schema.sql

3. **من سطر الأوامر:**
   mysql -h localhost -u md -p md3 < database/schema.sql

========================================

🗂️ هيكل قاعدة البيانات md3:

الجداول الرئيسية:
✅ companies - الشركات
✅ branches - الفروع (3 فروع تجريبية)
✅ point_of_sales - نقاط البيع (8 نقاط تجريبية)
✅ users - المستخدمين (4 مستخدمين)
✅ products - المنتجات (7 منتجات تجريبية)
✅ product_categories - فئات المنتجات (6 فئات)
✅ customers - العملاء
✅ suppliers - الموردين
✅ invoices - الفواتير
✅ invoice_items - تفاصيل الفواتير
✅ inventory - المخزون
✅ accounts - الحسابات المحاسبية
✅ journal_entries - القيود اليومية
✅ activity_logs - سجل الأنشطة
✅ backups - النسخ الاحتياطية
✅ settings - الإعدادات

========================================

🏪 البيانات التجريبية في md3:

الفروع:
1. فرع الزرقاء (ZRQ001)
2. فرع ماركا (MRK001)
3. فرع قفقفا (QFQ001)

نقاط البيع:
1. سوبر ماركت الزرقاء
2. كافتيريا الزرقاء
3. محمص الزرقاء
4. حلويات الزرقاء
5. مخبز الزرقاء
6. سوبر ماركت ماركا
7. كافتيريا ماركا
8. سوبر ماركت قفقفا

المستخدمين:
- admin (مدير النظام)
- main_accountant (محاسب رئيسي)
- zarqa_accountant (محاسب فرع الزرقاء)
- marka_accountant (محاسب فرع ماركا)

جميع كلمات المرور: password

========================================

🔧 التحقق من نجاح الإنشاء:

1. اختبار الاتصال:
   http://localhost/MD1/test_connection.php

2. تشخيص شامل:
   http://localhost/MD1/debug.php

3. اختبار الفروع:
   http://localhost/MD1/branches.php

4. اختبار نقاط البيع:
   http://localhost/MD1/pos.php

========================================

🚀 بعد إنشاء قاعدة البيانات:

1. سجل الدخول:
   http://localhost/MD1/login.php
   
   المدير: admin / password

2. ابدأ استخدام النظام:
   - إدارة الفروع ونقاط البيع
   - إدارة المنتجات والمخزون
   - إنشاء الفواتير
   - عرض التقارير

========================================

⚠️ ملاحظات مهمة:

1. تأكد من تشغيل MySQL في XAMPP
2. تأكد من صحة بيانات الاتصال
3. إذا كانت قاعدة البيانات موجودة مسبقاً، سيتم استخدامها
4. يمكن تغيير اسم قاعدة البيانات من ملف config/database.php

========================================

📞 للمساعدة:
- استخدم أدوات التشخيص المتوفرة
- راجع ملف error.log في XAMPP
- تأكد من الصلاحيات المناسبة

========================================
