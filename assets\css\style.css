/* نظام المحاسبة المالية - ملف الأنماط الرئيسي */

/* الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

/* المتغيرات الأساسية */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;

    --font-family: 'Cairo', sans-serif;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.3s ease;
}

/* الإعدادات العامة */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-size: 14px;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: #f5f5f5;
    direction: rtl;
    text-align: right;
}

/* تحسين النصوص العربية */
.arabic-text {
    font-family: var(--font-family);
    font-weight: 400;
    letter-spacing: 0.02em;
}

/* الروابط */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: #0056b3;
    text-decoration: none;
}

/* البطاقات */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    margin-bottom: 1.5rem;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    padding: 1rem 1.25rem;
}

.card-body {
    padding: 1.25rem;
}

/* الأزرار */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: var(--transition);
    border: none;
    cursor: pointer;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
}

.btn-success {
    background: linear-gradient(45deg, #28a745, #1e7e34);
}

.btn-danger {
    background: linear-gradient(45deg, #dc3545, #c82333);
}

.btn-warning {
    background: linear-gradient(45deg, #ffc107, #e0a800);
    color: #212529;
}

.btn-info {
    background: linear-gradient(45deg, #17a2b8, #138496);
}

/* النماذج */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    padding: 0.75rem;
    transition: var(--transition);
    font-family: var(--font-family);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.form-select {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    padding: 0.75rem;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: left 0.75rem center;
    background-size: 16px 12px;
}

/* الجداول */
.table {
    margin-bottom: 0;
    font-family: var(--font-family);
}

.table th {
    background-color: var(--light-color);
    font-weight: 600;
    border-top: none;
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

.table td {
    padding: 0.75rem;
    vertical-align: middle;
    border-top: 1px solid #dee2e6;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* الشارات */
.badge {
    font-size: 0.75em;
    font-weight: 500;
    padding: 0.35em 0.65em;
    border-radius: 0.25rem;
}

/* التنبيهات */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.25rem;
    margin-bottom: 1rem;
    font-family: var(--font-family);
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border-left: 4px solid var(--info-color);
}

/* الشريط الجانبي */
.sidebar {
    background-color: #ffffff;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
}

.sidebar .nav-link {
    color: #495057;
    font-weight: 500;
    padding: 0.75rem 1rem;
    margin: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.sidebar .nav-link:hover {
    background-color: rgba(0, 123, 255, 0.1);
    color: var(--primary-color);
}

.sidebar .nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
}

/* شريط التنقل العلوي */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 0.75rem 1rem;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
}

/* القوائم المنسدلة */
.dropdown-menu {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-radius: var(--border-radius);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: var(--transition);
}

.dropdown-item:hover {
    background-color: rgba(0, 123, 255, 0.1);
    color: var(--primary-color);
}

.dropdown-divider {
    margin: 0.5rem 0;
}

/* بطاقات الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: var(--transition);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.15);
}

.stats-card .stats-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.stats-card .stats-number {
    font-size: 2rem;
    font-weight: 700;
    margin: 0.5rem 0;
}

.stats-card .stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* بطاقات الحدود الملونة */
.border-right-primary {
    border-right: 4px solid var(--primary-color) !important;
}

.border-right-success {
    border-right: 4px solid var(--success-color) !important;
}

.border-right-info {
    border-right: 4px solid var(--info-color) !important;
}

.border-right-warning {
    border-right: 4px solid var(--warning-color) !important;
}

/* الرسوم البيانية */
.chart-area {
    position: relative;
    height: 300px;
}

.chart-pie {
    position: relative;
    height: 250px;
}

/* التحميل */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* الصفحات المطبوعة */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .no-print {
        display: none !important;
    }

    .container-fluid {
        margin: 0 !important;
        padding: 0 !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
}

/* الشاشات الصغيرة */
@media (max-width: 768px) {
    .sidebar {
        position: relative !important;
        height: auto !important;
        margin-top: 0 !important;
    }

    .stats-card {
        margin-bottom: 1rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
}

/* تحسينات إضافية */
.text-primary { color: var(--primary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-info { color: var(--info-color) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-info { background-color: var(--info-color) !important; }

/* تأثيرات الحركة */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* تحسين الأداء */
.card,
.btn,
.form-control,
.alert {
    will-change: transform;
}

/* تخصيص شريط التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* تحسين التباعد */
.mb-4 { margin-bottom: 1.5rem !important; }
.mt-4 { margin-top: 1.5rem !important; }
.py-4 { padding-top: 1.5rem !important; padding-bottom: 1.5rem !important; }
.px-4 { padding-left: 1.5rem !important; padding-right: 1.5rem !important; }

/* تحسين الخطوط */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 0.75rem;
}

.font-weight-bold { font-weight: 700 !important; }
.font-weight-normal { font-weight: 400 !important; }
.font-weight-light { font-weight: 300 !important; }

/* تحسين الظلال */
.shadow-sm { box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important; }
.shadow { box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important; }
.shadow-lg { box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important; }

/* إخفاء الشريط الأفقي فقط */
body, html {
    overflow-x: hidden !important;
}

.container-fluid {
    overflow-x: hidden !important;
}

.row {
    margin-left: 0 !important;
    margin-right: 0 !important;
}

/* تحسين المحتوى مع القائمة الجانبية */
@media (min-width: 768px) {
    .main-content-with-right-sidebar {
        margin-right: 300px;
        margin-left: 0;
        padding-right: 15px;
        padding-left: 15px;
        width: calc(100% - 300px);
        max-width: calc(100% - 300px);
        overflow-x: hidden !important;
        box-sizing: border-box;
    }
}

@media (max-width: 767.98px) {
    .main-content-with-right-sidebar {
        margin-right: 0;
        margin-left: 0;
        padding-right: 15px;
        padding-left: 15px;
        width: 100%;
        max-width: 100%;
        overflow-x: hidden !important;
        box-sizing: border-box;
    }
}

/* تحسين الجداول والعناصر العريضة */
.table-responsive {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.card, .form-control, .alert {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

/* تحسين الأعمدة */
.col, [class*="col-"] {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    box-sizing: border-box;
}

/* تحسين النصوص الطويلة */
.text-wrap, .text-break {
    word-wrap: break-word;
    word-break: break-word;
    overflow-wrap: break-word;
}

/* ضمان ظهور جميع البيانات */
.container-fluid .row {
    width: 100%;
    margin: 0;
}

.container-fluid .col-12 {
    width: 100%;
    padding-left: 0;
    padding-right: 0;
}

/* تحسين عرض الجداول */
.table {
    width: 100%;
    table-layout: auto;
}

.table th, .table td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

/* تحسين الأزرار */
.btn-group {
    flex-wrap: wrap;
}

.btn {
    margin: 2px;
}

/* تحسين النماذج */
.form-group {
    margin-bottom: 1rem;
}

.form-control {
    width: 100%;
}

/* تحسين البطاقات */
.card {
    margin-bottom: 1rem;
}

.card-body {
    padding: 1rem;
}


