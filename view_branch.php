<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

checkSession();

$user = getUserById($_SESSION['user_id']);
$branchId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$branchId) {
    header('Location: branches.php?error=invalid_id');
    exit();
}

// جلب بيانات الفرع
try {
    $sql = "SELECT b.*, c.name as company_name 
            FROM branches b 
            LEFT JOIN companies c ON b.company_id = c.id 
            WHERE b.id = ?";
    $branch = fetchOne($sql, [$branchId]);
    
    if (!$branch) {
        header('Location: branches.php?error=branch_not_found');
        exit();
    }
} catch (Exception $e) {
    header('Location: branches.php?error=database_error');
    exit();
}

// جلب نقاط البيع التابعة للفرع
try {
    $pointOfSales = fetchAll("SELECT * FROM point_of_sales WHERE branch_id = ? ORDER BY name", [$branchId]);
} catch (Exception $e) {
    $pointOfSales = [];
}

// جلب إحصائيات الفرع
try {
    // عدد المعاملات
    $totalTransactions = fetchOne("SELECT COUNT(*) as count FROM invoices WHERE branch_id = ?", [$branchId])['count'] ?? 0;
    
    // إجمالي المبيعات
    $totalSales = fetchOne("SELECT COALESCE(SUM(total_amount), 0) as total FROM invoices WHERE branch_id = ? AND type = 'sale'", [$branchId])['total'] ?? 0;
    
    // إجمالي المشتريات
    $totalPurchases = fetchOne("SELECT COALESCE(SUM(total_amount), 0) as total FROM invoices WHERE branch_id = ? AND type = 'purchase'", [$branchId])['total'] ?? 0;
    
    // المعاملات هذا الشهر
    $thisMonthTransactions = fetchOne("SELECT COUNT(*) as count FROM invoices WHERE branch_id = ? AND MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())", [$branchId])['count'] ?? 0;
    
} catch (Exception $e) {
    $totalTransactions = 0;
    $totalSales = 0;
    $totalPurchases = 0;
    $thisMonthTransactions = 0;
}

// جلب آخر المعاملات
try {
    $recentTransactions = fetchAll("
        SELECT i.*, u.full_name as user_name 
        FROM invoices i 
        LEFT JOIN users u ON i.user_id = u.id 
        WHERE i.branch_id = ? 
        ORDER BY i.created_at DESC 
        LIMIT 10", [$branchId]);
} catch (Exception $e) {
    $recentTransactions = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض الفرع - نظام المحاسبة المالية</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body { font-family: 'Cairo', sans-serif; }
        
        /* تخطيط القائمة على اليمين */
        @media (min-width: 768px) {
            .main-content-with-right-sidebar {
                margin-right: 300px;
                padding-right: 20px;
                padding-left: 20px;
            }
        }
        
        @media (max-width: 767.98px) {
            .main-content-with-right-sidebar {
                margin-right: 0;
                padding-right: 15px;
                padding-left: 15px;
            }
        }
        
        .branch-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 8px rgba(0,123,255,0.3);
        }
        
        .stat-card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .pos-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: white;
            transition: all 0.3s ease;
        }
        
        .pos-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .transaction-item {
            border-bottom: 1px solid #eee;
            padding: 10px 0;
        }
        
        .transaction-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>
    
    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Main Content Area -->
            <main class="col-12 px-md-4 main-content-with-right-sidebar">
                <!-- رأس الصفحة -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-store me-2"></i>
                        تفاصيل الفرع
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="branches.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right"></i> العودة للقائمة
                            </a>
                            <?php if (isAdmin($_SESSION['user_id'])): ?>
                            <a href="edit_branch.php?id=<?php echo $branchId; ?>" class="btn btn-outline-primary">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- معلومات الفرع الرئيسية -->
                <div class="branch-header">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-2">
                                <i class="fas fa-store me-3"></i>
                                <?php echo htmlspecialchars($branch['name']); ?>
                            </h2>
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="mb-1">
                                        <i class="fas fa-code me-2"></i>
                                        <strong>الكود:</strong> <?php echo htmlspecialchars($branch['code']); ?>
                                    </p>
                                    <?php if ($branch['company_name']): ?>
                                    <p class="mb-1">
                                        <i class="fas fa-building me-2"></i>
                                        <strong>الشركة:</strong> <?php echo htmlspecialchars($branch['company_name']); ?>
                                    </p>
                                    <?php endif; ?>
                                    <?php if ($branch['manager_name']): ?>
                                    <p class="mb-1">
                                        <i class="fas fa-user-tie me-2"></i>
                                        <strong>المدير:</strong> <?php echo htmlspecialchars($branch['manager_name']); ?>
                                    </p>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-6">
                                    <?php if ($branch['address']): ?>
                                    <p class="mb-1">
                                        <i class="fas fa-map-marker-alt me-2"></i>
                                        <strong>العنوان:</strong> <?php echo htmlspecialchars($branch['address']); ?>
                                    </p>
                                    <?php endif; ?>
                                    <?php if ($branch['phone']): ?>
                                    <p class="mb-1">
                                        <i class="fas fa-phone me-2"></i>
                                        <strong>الهاتف:</strong> <?php echo htmlspecialchars($branch['phone']); ?>
                                    </p>
                                    <?php endif; ?>
                                    <p class="mb-1">
                                        <i class="fas fa-calendar me-2"></i>
                                        <strong>تاريخ الإنشاء:</strong> <?php echo date('Y-m-d', strtotime($branch['created_at'])); ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="mb-3">
                                <?php if ($branch['is_active']): ?>
                                <span class="badge bg-success fs-6 px-3 py-2">
                                    <i class="fas fa-check-circle me-2"></i>
                                    فرع نشط
                                </span>
                                <?php else: ?>
                                <span class="badge bg-danger fs-6 px-3 py-2">
                                    <i class="fas fa-times-circle me-2"></i>
                                    فرع غير نشط
                                </span>
                                <?php endif; ?>
                            </div>
                            <div>
                                <h4 class="mb-0"><?php echo count($pointOfSales); ?></h4>
                                <small>نقطة بيع</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card stat-card border-primary">
                            <div class="card-body text-center">
                                <div class="text-primary mb-2">
                                    <i class="fas fa-file-invoice fa-2x"></i>
                                </div>
                                <h4 class="text-primary"><?php echo number_format($totalTransactions); ?></h4>
                                <p class="text-muted mb-0">إجمالي المعاملات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card stat-card border-success">
                            <div class="card-body text-center">
                                <div class="text-success mb-2">
                                    <i class="fas fa-arrow-up fa-2x"></i>
                                </div>
                                <h4 class="text-success"><?php echo number_format($totalSales, 2); ?></h4>
                                <p class="text-muted mb-0">إجمالي المبيعات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card stat-card border-warning">
                            <div class="card-body text-center">
                                <div class="text-warning mb-2">
                                    <i class="fas fa-arrow-down fa-2x"></i>
                                </div>
                                <h4 class="text-warning"><?php echo number_format($totalPurchases, 2); ?></h4>
                                <p class="text-muted mb-0">إجمالي المشتريات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card stat-card border-info">
                            <div class="card-body text-center">
                                <div class="text-info mb-2">
                                    <i class="fas fa-calendar-month fa-2x"></i>
                                </div>
                                <h4 class="text-info"><?php echo number_format($thisMonthTransactions); ?></h4>
                                <p class="text-muted mb-0">معاملات هذا الشهر</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- نقاط البيع -->
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-cash-register me-2"></i>
                                    نقاط البيع (<?php echo count($pointOfSales); ?>)
                                </h5>
                                <?php if (isAdmin($_SESSION['user_id'])): ?>
                                <a href="add_pos.php?branch_id=<?php echo $branchId; ?>" class="btn btn-sm btn-primary">
                                    <i class="fas fa-plus"></i> إضافة نقطة بيع
                                </a>
                                <?php endif; ?>
                            </div>
                            <div class="card-body">
                                <?php if (empty($pointOfSales)): ?>
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-cash-register fa-3x mb-3"></i>
                                    <p>لا توجد نقاط بيع مضافة بعد</p>
                                    <?php if (isAdmin($_SESSION['user_id'])): ?>
                                    <a href="add_pos.php?branch_id=<?php echo $branchId; ?>" class="btn btn-outline-primary">
                                        إضافة نقطة بيع جديدة
                                    </a>
                                    <?php endif; ?>
                                </div>
                                <?php else: ?>
                                <?php foreach ($pointOfSales as $pos): ?>
                                <div class="pos-card">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">
                                                <i class="fas fa-cash-register me-2 text-primary"></i>
                                                <?php echo htmlspecialchars($pos['name']); ?>
                                            </h6>
                                            <p class="text-muted mb-1">
                                                <small>
                                                    <i class="fas fa-tag me-1"></i>
                                                    <?php echo htmlspecialchars($pos['type']); ?>
                                                </small>
                                            </p>
                                            <?php if ($pos['description']): ?>
                                            <p class="text-muted mb-0">
                                                <small><?php echo htmlspecialchars($pos['description']); ?></small>
                                            </p>
                                            <?php endif; ?>
                                        </div>
                                        <div class="text-end">
                                            <?php if ($pos['is_active']): ?>
                                            <span class="badge bg-success mb-2">نشط</span>
                                            <?php else: ?>
                                            <span class="badge bg-danger mb-2">غير نشط</span>
                                            <?php endif; ?>
                                            <?php if (isAdmin($_SESSION['user_id'])): ?>
                                            <div class="btn-group-vertical btn-group-sm">
                                                <button class="btn btn-outline-primary btn-sm" onclick="viewPOS(<?php echo $pos['id']; ?>)">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-secondary btn-sm" onclick="editPOS(<?php echo $pos['id']; ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- آخر المعاملات -->
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-history me-2"></i>
                                    آخر المعاملات
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recentTransactions)): ?>
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-file-invoice fa-3x mb-3"></i>
                                    <p>لا توجد معاملات بعد</p>
                                </div>
                                <?php else: ?>
                                <div style="max-height: 400px; overflow-y: auto;">
                                    <?php foreach ($recentTransactions as $transaction): ?>
                                    <div class="transaction-item">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1">
                                                    <?php if ($transaction['type'] == 'sale'): ?>
                                                    <i class="fas fa-arrow-up text-success me-2"></i>
                                                    <?php else: ?>
                                                    <i class="fas fa-arrow-down text-warning me-2"></i>
                                                    <?php endif; ?>
                                                    فاتورة رقم <?php echo $transaction['invoice_number']; ?>
                                                </h6>
                                                <p class="text-muted mb-1">
                                                    <small>
                                                        <i class="fas fa-user me-1"></i>
                                                        <?php echo htmlspecialchars($transaction['user_name'] ?? 'غير محدد'); ?>
                                                    </small>
                                                </p>
                                                <p class="text-muted mb-0">
                                                    <small>
                                                        <i class="fas fa-calendar me-1"></i>
                                                        <?php echo date('Y-m-d H:i', strtotime($transaction['created_at'])); ?>
                                                    </small>
                                                </p>
                                            </div>
                                            <div class="text-end">
                                                <h6 class="mb-0 <?php echo $transaction['type'] == 'sale' ? 'text-success' : 'text-warning'; ?>">
                                                    <?php echo number_format($transaction['total_amount'], 2); ?> د.أ
                                                </h6>
                                                <small class="text-muted">
                                                    <?php echo $transaction['type'] == 'sale' ? 'مبيعات' : 'مشتريات'; ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                                <div class="text-center mt-3">
                                    <a href="reports.php?branch_id=<?php echo $branchId; ?>" class="btn btn-outline-primary btn-sm">
                                        عرض جميع المعاملات
                                    </a>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إجراءات سريعة -->
                <?php if (isAdmin($_SESSION['user_id'])): ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-tools me-2"></i>
                            إجراءات سريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <a href="add_pos.php?branch_id=<?php echo $branchId; ?>" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-plus me-2"></i>
                                    إضافة نقطة بيع
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="edit_branch.php?id=<?php echo $branchId; ?>" class="btn btn-outline-secondary w-100">
                                    <i class="fas fa-edit me-2"></i>
                                    تعديل الفرع
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="reports.php?branch_id=<?php echo $branchId; ?>" class="btn btn-outline-info w-100">
                                    <i class="fas fa-chart-line me-2"></i>
                                    التقارير
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="users.php?branch_id=<?php echo $branchId; ?>" class="btn btn-outline-success w-100">
                                    <i class="fas fa-users me-2"></i>
                                    المستخدمين
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </main>
            
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function viewPOS(id) {
            window.location.href = 'view_pos.php?id=' + id;
        }
        
        function editPOS(id) {
            window.location.href = 'edit_pos.php?id=' + id;
        }
        
        // إضافة تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير على البطاقات
            document.querySelectorAll('.stat-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
                });
            });
        });
    </script>
</body>
</html>
