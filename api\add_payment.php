<?php
header('Content-Type: application/json; charset=utf-8');
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit();
}

try {
    $invoiceId = $_POST['invoice_id'] ?? '';
    $amount = $_POST['payment_amount'] ?? '';
    $method = $_POST['payment_method'] ?? '';
    $date = $_POST['payment_date'] ?? '';
    $referenceNumber = $_POST['reference_number'] ?? '';
    $notes = $_POST['payment_notes'] ?? '';
    
    // التحقق من صحة البيانات
    if (empty($invoiceId) || empty($amount) || empty($method) || empty($date)) {
        throw new Exception('يرجى ملء جميع الحقول المطلوبة');
    }
    
    $amount = (float)$amount;
    if ($amount <= 0) {
        throw new Exception('مبلغ الدفعة يجب أن يكون أكبر من صفر');
    }
    
    // التحقق من وجود الفاتورة
    $invoice = fetchOne("SELECT * FROM invoices WHERE id = ?", [$invoiceId]);
    if (!$invoice) {
        throw new Exception('الفاتورة غير موجودة');
    }
    
    // التحقق من صلاحية الوصول للفاتورة
    $user = getUserById($_SESSION['user_id']);
    if ($user['user_type'] === 'branch_accountant' && $user['branch_id'] && $user['branch_id'] != $invoice['branch_id']) {
        throw new Exception('غير مصرح لك بإضافة دفعات لهذه الفاتورة');
    }
    
    // التحقق من أن المبلغ لا يتجاوز المبلغ المتبقي
    if ($amount > $invoice['remaining_amount']) {
        throw new Exception('مبلغ الدفعة يتجاوز المبلغ المتبقي (' . number_format($invoice['remaining_amount'], 2) . ' د.أ)');
    }
    
    // بدء المعاملة
    beginTransaction();
    
    try {
        // إضافة الدفعة
        $sql = "INSERT INTO payments (invoice_id, payment_method, amount, payment_date, reference_number, notes, user_id) 
                VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        executeQuery($sql, [
            $invoiceId,
            $method,
            $amount,
            $date,
            $referenceNumber,
            $notes,
            $_SESSION['user_id']
        ]);
        
        // تحديث الفاتورة
        $newPaidAmount = $invoice['paid_amount'] + $amount;
        $newRemainingAmount = $invoice['total_amount'] - $newPaidAmount;
        $newStatus = $newRemainingAmount <= 0 ? 'paid' : $invoice['status'];
        
        $updateSql = "UPDATE invoices 
                      SET paid_amount = ?, remaining_amount = ?, status = ?, updated_at = NOW() 
                      WHERE id = ?";
        
        executeQuery($updateSql, [$newPaidAmount, $newRemainingAmount, $newStatus, $invoiceId]);
        
        // تسجيل النشاط
        logActivity(
            $_SESSION['user_id'], 
            'إضافة دفعة', 
            "تم إضافة دفعة بمبلغ $amount د.أ للفاتورة رقم " . $invoice['invoice_number'],
            'payments',
            getLastInsertId()
        );
        
        commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم إضافة الدفعة بنجاح',
            'payment_id' => getLastInsertId(),
            'new_paid_amount' => $newPaidAmount,
            'new_remaining_amount' => $newRemainingAmount,
            'new_status' => $newStatus
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
