<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

checkSession();
$user = getUserById($_SESSION['user_id']);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التخطيط المحسن - نظام المحاسبة المالية</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body { 
            font-family: 'Cairo', sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-header {
            background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.9));
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .test-section {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .layout-demo {
            border: 2px dashed #007bff;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            background: rgba(0, 123, 255, 0.05);
            position: relative;
        }
        
        .layout-demo::before {
            content: "منطقة المحتوى";
            position: absolute;
            top: -10px;
            left: 20px;
            background: white;
            padding: 0 10px;
            color: #007bff;
            font-weight: bold;
            font-size: 12px;
        }
        
        .sidebar-demo {
            position: fixed;
            top: 70px;
            right: 0;
            width: 280px;
            height: 200px;
            background: rgba(248, 249, 250, 0.9);
            border: 2px solid #28a745;
            border-radius: 8px 0 0 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            color: #28a745;
            font-weight: bold;
        }
        
        .measurement {
            background: #ffc107;
            color: #000;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin: 5px;
        }
        
        .success-badge {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            margin: 5px;
        }
        
        .wide-content {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #6c757d;
        }
        
        .responsive-test {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .responsive-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>
    
    <!-- Demo Sidebar Indicator -->
    <div class="sidebar-demo d-none d-md-flex">
        <div>
            <i class="fas fa-arrow-left me-2"></i>
            القائمة الجانبية
            <br>
            <small>280px عرض</small>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Main Content Area -->
            <main class="col-12 px-md-4 main-content-with-right-sidebar">
                <!-- Test Header -->
                <div class="test-header">
                    <h1 class="display-5 mb-3">
                        <i class="fas fa-check-circle text-success me-3"></i>
                        تم إصلاح التخطيط بنجاح!
                    </h1>
                    <p class="lead mb-3">
                        الآن الصفحة تظهر كاملة بدون شريط تمرير أفقي مع القائمة الجانبية على اليمين
                    </p>
                    <div class="success-badge">
                        <i class="fas fa-desktop me-2"></i>
                        تخطيط محسن ومتجاوب
                    </div>
                </div>

                <!-- Layout Measurements -->
                <div class="test-section">
                    <h2 class="mb-4">
                        <i class="fas fa-ruler text-primary me-2"></i>
                        قياسات التخطيط
                    </h2>
                    
                    <div class="layout-demo">
                        <h5>منطقة المحتوى الرئيسي</h5>
                        <p>هذه المنطقة تأخذ العرض الكامل للشاشة ناقص عرض القائمة الجانبية</p>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="measurement">العرض الكلي: 100vw</div>
                                <div class="measurement">عرض القائمة: 280px</div>
                                <div class="measurement">عرض المحتوى: calc(100vw - 280px)</div>
                            </div>
                            <div class="col-md-6">
                                <div class="measurement">الهامش الأيمن: 280px</div>
                                <div class="measurement">الحشو الداخلي: 15px</div>
                                <div class="measurement">لا يوجد تمرير أفقي</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Responsive Test -->
                <div class="test-section">
                    <h2 class="mb-4">
                        <i class="fas fa-mobile-alt text-success me-2"></i>
                        اختبار الاستجابة
                    </h2>
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>كيف يعمل التخطيط المتجاوب؟</h6>
                        <ul class="mb-0">
                            <li><strong>الشاشات الكبيرة (≥768px):</strong> القائمة على اليمين، المحتوى يأخذ باقي المساحة</li>
                            <li><strong>الشاشات الصغيرة (<768px):</strong> القائمة أعلى المحتوى، المحتوى يأخذ العرض الكامل</li>
                            <li><strong>جميع الشاشات:</strong> لا يوجد تمرير أفقي</li>
                        </ul>
                    </div>
                    
                    <div class="responsive-test">
                        <div class="responsive-item">
                            <i class="fas fa-desktop fa-2x text-primary mb-2"></i>
                            <h6>شاشة كبيرة</h6>
                            <small>القائمة على اليمين</small>
                        </div>
                        <div class="responsive-item">
                            <i class="fas fa-tablet-alt fa-2x text-success mb-2"></i>
                            <h6>شاشة متوسطة</h6>
                            <small>تخطيط متكيف</small>
                        </div>
                        <div class="responsive-item">
                            <i class="fas fa-mobile-alt fa-2x text-warning mb-2"></i>
                            <h6>شاشة صغيرة</h6>
                            <small>القائمة أعلى المحتوى</small>
                        </div>
                        <div class="responsive-item">
                            <i class="fas fa-ban fa-2x text-danger mb-2"></i>
                            <h6>لا تمرير أفقي</h6>
                            <small>في جميع الشاشات</small>
                        </div>
                    </div>
                </div>

                <!-- Content Width Test -->
                <div class="test-section">
                    <h2 class="mb-4">
                        <i class="fas fa-expand-arrows-alt text-info me-2"></i>
                        اختبار عرض المحتوى
                    </h2>
                    
                    <div class="wide-content">
                        <h5>محتوى عريض للاختبار</h5>
                        <p>هذا النص طويل جداً لاختبار أن المحتوى لا يتجاوز حدود الشاشة ولا يسبب ظهور شريط تمرير أفقي. يجب أن يظهر هذا النص بالكامل داخل منطقة المحتوى المخصصة دون تداخل مع القائمة الجانبية.</p>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-body">
                                        <h6>عمود 1</h6>
                                        <p>محتوى العمود الأول</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-body">
                                        <h6>عمود 2</h6>
                                        <p>محتوى العمود الثاني</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-body">
                                        <h6>عمود 3</h6>
                                        <p>محتوى العمود الثالث</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Table Test -->
                <div class="test-section">
                    <h2 class="mb-4">
                        <i class="fas fa-table text-warning me-2"></i>
                        اختبار الجداول
                    </h2>
                    
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>العنصر</th>
                                    <th>القيمة القديمة</th>
                                    <th>القيمة الجديدة</th>
                                    <th>الحالة</th>
                                    <th>ملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>عرض القائمة</td>
                                    <td>250px</td>
                                    <td>280px</td>
                                    <td><span class="badge bg-success">محسن</span></td>
                                    <td>عرض أفضل للقائمة</td>
                                </tr>
                                <tr>
                                    <td>هامش المحتوى</td>
                                    <td>300px</td>
                                    <td>280px</td>
                                    <td><span class="badge bg-success">محسن</span></td>
                                    <td>مساحة أكبر للمحتوى</td>
                                </tr>
                                <tr>
                                    <td>التمرير الأفقي</td>
                                    <td>موجود</td>
                                    <td>مخفي</td>
                                    <td><span class="badge bg-success">مُصلح</span></td>
                                    <td>لا يظهر شريط أفقي</td>
                                </tr>
                                <tr>
                                    <td>الاستجابة</td>
                                    <td>جيد</td>
                                    <td>ممتاز</td>
                                    <td><span class="badge bg-success">محسن</span></td>
                                    <td>يعمل على جميع الشاشات</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Form Test -->
                <div class="test-section">
                    <h2 class="mb-4">
                        <i class="fas fa-edit text-secondary me-2"></i>
                        اختبار النماذج
                    </h2>
                    
                    <form>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">حقل نصي طويل</label>
                                    <input type="text" class="form-control" placeholder="هذا حقل نصي طويل لاختبار عدم تجاوز حدود المحتوى">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">قائمة منسدلة</label>
                                    <select class="form-select">
                                        <option>خيار طويل جداً لاختبار عدم تجاوز الحدود</option>
                                        <option>خيار آخر</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">منطقة نص</label>
                            <textarea class="form-control" rows="3" placeholder="منطقة نص لاختبار أن المحتوى لا يتجاوز حدود الشاشة ولا يسبب ظهور شريط تمرير أفقي"></textarea>
                        </div>
                        
                        <div class="text-center">
                            <button type="button" class="btn btn-primary me-2">زر اختبار</button>
                            <button type="button" class="btn btn-success me-2">زر آخر</button>
                            <button type="button" class="btn btn-info">زر ثالث</button>
                        </div>
                    </form>
                </div>

                <!-- Success Summary -->
                <div class="test-section">
                    <h2 class="mb-4">
                        <i class="fas fa-trophy text-warning me-2"></i>
                        ملخص الإصلاحات
                    </h2>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="text-success">✅ تم إصلاحه:</h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">إخفاء الشريط الأفقي تماماً</li>
                                <li class="list-group-item">تحسين عرض القائمة الجانبية</li>
                                <li class="list-group-item">تحسين مساحة المحتوى</li>
                                <li class="list-group-item">منع تجاوز العناصر للحدود</li>
                                <li class="list-group-item">تحسين الاستجابة للشاشات المختلفة</li>
                            </ul>
                        </div>
                        
                        <div class="col-md-6">
                            <h5 class="text-primary">🎯 النتائج:</h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">صفحة كاملة بدون شريط أفقي</li>
                                <li class="list-group-item">قائمة جانبية ثابتة على اليمين</li>
                                <li class="list-group-item">محتوى يستغل المساحة المتاحة</li>
                                <li class="list-group-item">تخطيط متجاوب ومرن</li>
                                <li class="list-group-item">تجربة مستخدم محسنة</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Navigation Buttons -->
                <div class="text-center mt-4">
                    <a href="index.php" class="btn btn-primary me-2">
                        <i class="fas fa-home me-2"></i>
                        الصفحة الرئيسية
                    </a>
                    <a href="branches.php" class="btn btn-success me-2">
                        <i class="fas fa-store me-2"></i>
                        إدارة الفروع
                    </a>
                    <a href="add_branch.php" class="btn btn-info me-2">
                        <i class="fas fa-plus me-2"></i>
                        إضافة فرع
                    </a>
                    <a href="test_auto_code.php" class="btn btn-warning">
                        <i class="fas fa-magic me-2"></i>
                        اختبار الكود التلقائي
                    </a>
                </div>
            </main>
            
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // إخفاء مؤشر القائمة التجريبي بعد 5 ثوان
        setTimeout(function() {
            const demoSidebar = document.querySelector('.sidebar-demo');
            if (demoSidebar) {
                demoSidebar.style.opacity = '0';
                demoSidebar.style.transition = 'opacity 1s ease';
                setTimeout(() => demoSidebar.remove(), 1000);
            }
        }, 5000);
        
        // فحص عرض الشاشة وعرض معلومات
        function checkScreenWidth() {
            const width = window.innerWidth;
            const info = document.createElement('div');
            info.style.cssText = `
                position: fixed;
                top: 10px;
                left: 10px;
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 10px;
                border-radius: 5px;
                z-index: 2000;
                font-size: 12px;
            `;
            info.innerHTML = `عرض الشاشة: ${width}px`;
            document.body.appendChild(info);
            
            setTimeout(() => info.remove(), 3000);
        }
        
        // عرض معلومات الشاشة عند تحميل الصفحة
        window.addEventListener('load', checkScreenWidth);
        window.addEventListener('resize', checkScreenWidth);
        
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير على البطاقات
            document.querySelectorAll('.test-section').forEach((section, index) => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(30px)';
                
                setTimeout(() => {
                    section.style.transition = 'all 0.6s ease';
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
