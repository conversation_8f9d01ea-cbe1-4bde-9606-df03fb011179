<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

checkSession();

$user = getUserById($_SESSION['user_id']);
$branches = getAllBranches();
$customers = fetchAll("SELECT * FROM customers WHERE is_active = 1 ORDER BY name");
$products = fetchAll("SELECT * FROM products WHERE is_active = 1 ORDER BY name");

// إذا كان محاسب فرعي، فلتر الفروع
if ($user['user_type'] === 'branch_accountant' && $user['branch_id']) {
    $branches = [$user['branch_id'] => getBranchById($user['branch_id'])];
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // التحقق من صحة البيانات
        $branchId = $_POST['branch_id'] ?? '';
        $posId = $_POST['pos_id'] ?? null;
        $customerId = $_POST['customer_id'] ?? null;
        $invoiceDate = $_POST['invoice_date'] ?? date('Y-m-d');
        $dueDate = $_POST['due_date'] ?? null;
        $notes = $_POST['notes'] ?? '';
        $items = $_POST['items'] ?? [];
        
        if (empty($branchId) || empty($items)) {
            throw new Exception('يرجى ملء جميع الحقول المطلوبة');
        }
        
        // التحقق من صلاحية الفرع للمحاسب الفرعي
        if ($user['user_type'] === 'branch_accountant' && $user['branch_id'] && $branchId != $user['branch_id']) {
            throw new Exception('غير مصرح لك بإنشاء فواتير لهذا الفرع');
        }
        
        // حساب الإجماليات
        $subtotal = 0;
        $taxAmount = 0;
        $discountAmount = 0;
        $processedItems = [];
        
        foreach ($items as $item) {
            if (empty($item['product_id']) || empty($item['quantity']) || empty($item['unit_price'])) {
                continue;
            }
            
            $quantity = (float)$item['quantity'];
            $unitPrice = (float)$item['unit_price'];
            $discountPercentage = (float)($item['discount_percentage'] ?? 0);
            $taxPercentage = (float)($item['tax_percentage'] ?? 0);
            
            $lineTotal = $quantity * $unitPrice;
            $lineDiscount = $lineTotal * ($discountPercentage / 100);
            $lineTax = ($lineTotal - $lineDiscount) * ($taxPercentage / 100);
            $lineAmount = $lineTotal - $lineDiscount + $lineTax;
            
            $processedItems[] = [
                'product_id' => $item['product_id'],
                'quantity' => $quantity,
                'unit_price' => $unitPrice,
                'discount_percentage' => $discountPercentage,
                'discount_amount' => $lineDiscount,
                'tax_percentage' => $taxPercentage,
                'tax_amount' => $lineTax,
                'total_amount' => $lineAmount
            ];
            
            $subtotal += $lineTotal;
            $discountAmount += $lineDiscount;
            $taxAmount += $lineTax;
        }
        
        if (empty($processedItems)) {
            throw new Exception('يجب إضافة منتج واحد على الأقل');
        }
        
        $totalAmount = $subtotal - $discountAmount + $taxAmount;
        $paidAmount = (float)($_POST['paid_amount'] ?? 0);
        
        // إنشاء الفاتورة
        $invoiceData = [
            'invoice_type' => 'sale',
            'branch_id' => $branchId,
            'pos_id' => $posId,
            'customer_id' => $customerId,
            'user_id' => $_SESSION['user_id'],
            'invoice_date' => $invoiceDate,
            'due_date' => $dueDate,
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'discount_amount' => $discountAmount,
            'total_amount' => $totalAmount,
            'paid_amount' => $paidAmount,
            'status' => $paidAmount >= $totalAmount ? 'paid' : 'pending',
            'notes' => $notes,
            'items' => $processedItems
        ];
        
        $invoiceId = createInvoice($invoiceData);
        
        $success = 'تم إنشاء الفاتورة بنجاح';
        
        // إعادة توجيه إلى صفحة عرض الفاتورة
        header("Location: view_invoice.php?id=$invoiceId&success=1");
        exit();
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة مبيعات جديدة - نظام المحاسبة المالية</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include '../../includes/navbar.php'; ?>
    
    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>
            
            <!-- Main Content Area -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-file-invoice-dollar me-2"></i>
                        فاتورة مبيعات جديدة
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="sales.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right"></i> العودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>

                <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
                <?php endif; ?>

                <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
                <?php endif; ?>

                <!-- نموذج الفاتورة -->
                <form method="POST" id="invoiceForm">
                    <div class="row">
                        <!-- معلومات الفاتورة -->
                        <div class="col-lg-8">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-info-circle me-2"></i>
                                        معلومات الفاتورة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="branch_id" class="form-label">الفرع *</label>
                                                <select class="form-select" id="branch_id" name="branch_id" required>
                                                    <option value="">اختر الفرع</option>
                                                    <?php foreach ($branches as $branch): ?>
                                                    <option value="<?php echo $branch['id']; ?>">
                                                        <?php echo htmlspecialchars($branch['name']); ?>
                                                    </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="pos_id" class="form-label">نقطة البيع</label>
                                                <select class="form-select" id="pos_id" name="pos_id">
                                                    <option value="">اختر نقطة البيع</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="customer_id" class="form-label">العميل</label>
                                                <select class="form-select" id="customer_id" name="customer_id">
                                                    <option value="">عميل نقدي</option>
                                                    <?php foreach ($customers as $customer): ?>
                                                    <option value="<?php echo $customer['id']; ?>">
                                                        <?php echo htmlspecialchars($customer['name']); ?>
                                                    </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="mb-3">
                                                <label for="invoice_date" class="form-label">تاريخ الفاتورة *</label>
                                                <input type="date" class="form-control" id="invoice_date" name="invoice_date" 
                                                       value="<?php echo date('Y-m-d'); ?>" required>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="mb-3">
                                                <label for="due_date" class="form-label">تاريخ الاستحقاق</label>
                                                <input type="date" class="form-control" id="due_date" name="due_date">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- أصناف الفاتورة -->
                            <div class="card mb-4">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">
                                        <i class="fas fa-boxes me-2"></i>
                                        أصناف الفاتورة
                                    </h5>
                                    <button type="button" class="btn btn-primary btn-sm" onclick="addItem()">
                                        <i class="fas fa-plus"></i> إضافة صنف
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered" id="itemsTable">
                                            <thead>
                                                <tr>
                                                    <th width="30%">المنتج</th>
                                                    <th width="10%">الكمية</th>
                                                    <th width="15%">سعر الوحدة</th>
                                                    <th width="10%">خصم %</th>
                                                    <th width="10%">ضريبة %</th>
                                                    <th width="15%">الإجمالي</th>
                                                    <th width="10%">إجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody id="itemsTableBody">
                                                <!-- سيتم إضافة الأصناف هنا بواسطة JavaScript -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- ملاحظات -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-sticky-note me-2"></i>
                                        ملاحظات
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <textarea class="form-control" id="notes" name="notes" rows="3" 
                                              placeholder="ملاحظات إضافية..."></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- ملخص الفاتورة -->
                        <div class="col-lg-4">
                            <div class="card sticky-top">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-calculator me-2"></i>
                                        ملخص الفاتورة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-2">
                                        <div class="col-6">المجموع الفرعي:</div>
                                        <div class="col-6 text-end">
                                            <span id="subtotalDisplay">0.00</span> د.أ
                                        </div>
                                    </div>
                                    <div class="row mb-2">
                                        <div class="col-6">إجمالي الخصم:</div>
                                        <div class="col-6 text-end">
                                            <span id="discountDisplay">0.00</span> د.أ
                                        </div>
                                    </div>
                                    <div class="row mb-2">
                                        <div class="col-6">إجمالي الضريبة:</div>
                                        <div class="col-6 text-end">
                                            <span id="taxDisplay">0.00</span> د.أ
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>الإجمالي النهائي:</strong></div>
                                        <div class="col-6 text-end">
                                            <strong><span id="totalDisplay">0.00</span> د.أ</strong>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="paid_amount" class="form-label">المبلغ المدفوع</label>
                                        <input type="number" class="form-control" id="paid_amount" name="paid_amount" 
                                               step="0.01" min="0" value="0">
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-6">المبلغ المتبقي:</div>
                                        <div class="col-6 text-end">
                                            <span id="remainingDisplay">0.00</span> د.أ
                                        </div>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>
                                            حفظ الفاتورة
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                                            <i class="fas fa-undo me-2"></i>
                                            إعادة تعيين
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    <script>
        let itemCounter = 0;
        const products = <?php echo json_encode($products); ?>;
        
        $(document).ready(function() {
            // تهيئة Select2
            $('#customer_id').select2({
                placeholder: 'اختر العميل أو اتركه فارغاً للعميل النقدي',
                allowClear: true
            });
            
            // إضافة صنف افتراضي
            addItem();
            
            // تحديث نقاط البيع عند تغيير الفرع
            $('#branch_id').change(function() {
                const branchId = $(this).val();
                updatePointOfSales(branchId);
            });
            
            // تحديث المبلغ المتبقي عند تغيير المبلغ المدفوع
            $('#paid_amount').on('input', updateRemaining);
        });
        
        function updatePointOfSales(branchId) {
            const posSelect = $('#pos_id');
            posSelect.empty().append('<option value="">اختر نقطة البيع</option>');
            
            if (branchId) {
                fetch(`../../api/get_pos.php?branch_id=${branchId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            data.pos.forEach(pos => {
                                posSelect.append(`<option value="${pos.id}">${pos.name}</option>`);
                            });
                        }
                    })
                    .catch(error => console.error('Error:', error));
            }
        }
        
        function addItem() {
            itemCounter++;
            const tbody = document.getElementById('itemsTableBody');
            const row = document.createElement('tr');
            row.id = `item-${itemCounter}`;
            
            row.innerHTML = `
                <td>
                    <select class="form-select product-select" name="items[${itemCounter}][product_id]" required onchange="updatePrice(${itemCounter})">
                        <option value="">اختر المنتج</option>
                        ${products.map(product => `<option value="${product.id}" data-price="${product.selling_price}">${product.name}</option>`).join('')}
                    </select>
                </td>
                <td>
                    <input type="number" class="form-control quantity-input" name="items[${itemCounter}][quantity]" 
                           step="0.01" min="0.01" value="1" required onchange="calculateItemTotal(${itemCounter})">
                </td>
                <td>
                    <input type="number" class="form-control price-input" name="items[${itemCounter}][unit_price]" 
                           step="0.01" min="0" required onchange="calculateItemTotal(${itemCounter})">
                </td>
                <td>
                    <input type="number" class="form-control discount-input" name="items[${itemCounter}][discount_percentage]" 
                           step="0.01" min="0" max="100" value="0" onchange="calculateItemTotal(${itemCounter})">
                </td>
                <td>
                    <input type="number" class="form-control tax-input" name="items[${itemCounter}][tax_percentage]" 
                           step="0.01" min="0" max="100" value="0" onchange="calculateItemTotal(${itemCounter})">
                </td>
                <td>
                    <span class="item-total">0.00</span> د.أ
                </td>
                <td>
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeItem(${itemCounter})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            
            tbody.appendChild(row);
        }
        
        function removeItem(itemId) {
            const row = document.getElementById(`item-${itemId}`);
            if (row) {
                row.remove();
                calculateTotals();
            }
        }
        
        function updatePrice(itemId) {
            const select = document.querySelector(`#item-${itemId} .product-select`);
            const priceInput = document.querySelector(`#item-${itemId} .price-input`);
            
            if (select.selectedOptions.length > 0) {
                const price = select.selectedOptions[0].dataset.price;
                priceInput.value = price;
                calculateItemTotal(itemId);
            }
        }
        
        function calculateItemTotal(itemId) {
            const row = document.getElementById(`item-${itemId}`);
            const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
            const price = parseFloat(row.querySelector('.price-input').value) || 0;
            const discountPercentage = parseFloat(row.querySelector('.discount-input').value) || 0;
            const taxPercentage = parseFloat(row.querySelector('.tax-input').value) || 0;
            
            const lineTotal = quantity * price;
            const discountAmount = lineTotal * (discountPercentage / 100);
            const taxAmount = (lineTotal - discountAmount) * (taxPercentage / 100);
            const itemTotal = lineTotal - discountAmount + taxAmount;
            
            row.querySelector('.item-total').textContent = itemTotal.toFixed(2);
            
            calculateTotals();
        }
        
        function calculateTotals() {
            let subtotal = 0;
            let totalDiscount = 0;
            let totalTax = 0;
            
            document.querySelectorAll('#itemsTableBody tr').forEach(row => {
                const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
                const price = parseFloat(row.querySelector('.price-input').value) || 0;
                const discountPercentage = parseFloat(row.querySelector('.discount-input').value) || 0;
                const taxPercentage = parseFloat(row.querySelector('.tax-input').value) || 0;
                
                const lineTotal = quantity * price;
                const discountAmount = lineTotal * (discountPercentage / 100);
                const taxAmount = (lineTotal - discountAmount) * (taxPercentage / 100);
                
                subtotal += lineTotal;
                totalDiscount += discountAmount;
                totalTax += taxAmount;
            });
            
            const total = subtotal - totalDiscount + totalTax;
            
            document.getElementById('subtotalDisplay').textContent = subtotal.toFixed(2);
            document.getElementById('discountDisplay').textContent = totalDiscount.toFixed(2);
            document.getElementById('taxDisplay').textContent = totalTax.toFixed(2);
            document.getElementById('totalDisplay').textContent = total.toFixed(2);
            
            updateRemaining();
        }
        
        function updateRemaining() {
            const total = parseFloat(document.getElementById('totalDisplay').textContent) || 0;
            const paid = parseFloat(document.getElementById('paid_amount').value) || 0;
            const remaining = total - paid;
            
            document.getElementById('remainingDisplay').textContent = remaining.toFixed(2);
        }
        
        function resetForm() {
            if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟ سيتم فقدان جميع البيانات المدخلة.')) {
                document.getElementById('invoiceForm').reset();
                document.getElementById('itemsTableBody').innerHTML = '';
                itemCounter = 0;
                addItem();
                calculateTotals();
            }
        }
        
        // التحقق من صحة النموذج قبل الإرسال
        document.getElementById('invoiceForm').addEventListener('submit', function(e) {
            const items = document.querySelectorAll('#itemsTableBody tr');
            if (items.length === 0) {
                e.preventDefault();
                alert('يجب إضافة منتج واحد على الأقل');
                return false;
            }
            
            let hasValidItem = false;
            items.forEach(row => {
                const productSelect = row.querySelector('.product-select');
                const quantity = row.querySelector('.quantity-input');
                const price = row.querySelector('.price-input');
                
                if (productSelect.value && quantity.value && price.value) {
                    hasValidItem = true;
                }
            });
            
            if (!hasValidItem) {
                e.preventDefault();
                alert('يجب إضافة منتج صحيح واحد على الأقل');
                return false;
            }
        });
    </script>
</body>
</html>
