<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

checkSession();
$user = getUserById($_SESSION['user_id']);

// التحقق من صلاحيات المدير
if (!isAdmin($_SESSION['user_id'])) {
    header("Location: index.php");
    exit();
}

$error = '';
$success = '';

// معالجة إضافة مستخدم جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_user') {
    try {
        $username = trim($_POST['username']);
        $password = trim($_POST['password']);
        $fullName = trim($_POST['full_name']);
        $email = trim($_POST['email']);
        $phone = trim($_POST['phone']);
        $userType = $_POST['user_type'];
        $branchId = !empty($_POST['branch_id']) ? $_POST['branch_id'] : null;
        $department = trim($_POST['department']);
        
        // التحقق من البيانات
        if (empty($username) || empty($password) || empty($fullName) || empty($userType)) {
            throw new Exception('يرجى ملء جميع الحقول المطلوبة');
        }
        
        if (strlen($password) < 6) {
            throw new Exception('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
        }
        
        // التحقق من عدم تكرار اسم المستخدم
        $existingUser = fetchOne("SELECT id FROM users WHERE username = ?", [$username]);
        if ($existingUser) {
            throw new Exception('اسم المستخدم موجود مسبقاً');
        }
        
        // تشفير كلمة المرور
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        
        // إدراج المستخدم الجديد
        $sql = "INSERT INTO users (username, password, full_name, email, phone, user_type, branch_id, department, is_active, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, TRUE, NOW())";
        
        executeQuery($sql, [$username, $hashedPassword, $fullName, $email, $phone, $userType, $branchId, $department]);
        
        $userId = getLastInsertId();
        
        // تسجيل النشاط
        logActivity($_SESSION['user_id'], 'إضافة مستخدم', "تم إضافة مستخدم جديد: $fullName ($username)", 'users', $userId);
        
        $success = "تم إضافة المستخدم بنجاح! اسم المستخدم: $username";
        
        // مسح البيانات بعد النجاح
        $_POST = [];
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// معالجة تعديل حالة المستخدم
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'toggle_status') {
    try {
        $userId = $_POST['user_id'];
        $currentStatus = $_POST['current_status'];
        $newStatus = $currentStatus ? 0 : 1;
        
        executeQuery("UPDATE users SET is_active = ? WHERE id = ?", [$newStatus, $userId]);
        
        $statusText = $newStatus ? 'تفعيل' : 'إلغاء تفعيل';
        logActivity($_SESSION['user_id'], 'تعديل حالة مستخدم', "تم $statusText المستخدم ID: $userId", 'users', $userId);
        
        $success = "تم تحديث حالة المستخدم بنجاح";
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// جلب جميع المستخدمين
$users = fetchAll("
    SELECT u.*, b.name as branch_name 
    FROM users u 
    LEFT JOIN branches b ON u.branch_id = b.id 
    ORDER BY u.created_at DESC
");

// جلب قائمة الفروع
$branches = fetchAll("SELECT id, name, code FROM branches WHERE is_active = 1 ORDER BY name");

// أنواع المستخدمين
$userTypes = [
    'admin' => 'مدير النظام',
    'main_accountant' => 'محاسب رئيسي',
    'branch_manager' => 'مدير فرع',
    'financial_manager' => 'رئيس فرع مالي',
    'department_head' => 'رئيس قسم',
    'accountant' => 'محاسب',
    'cashier' => 'أمين صندوق',
    'employee' => 'موظف'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - نظام المحاسبة المالية</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body { 
            font-family: 'Cairo', sans-serif; 
            background-color: #f8f9fa;
        }
        
        .user-card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .user-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        
        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        
        .status-badge {
            position: absolute;
            top: 10px;
            left: 10px;
        }
        
        .user-type-badge {
            font-size: 0.8em;
            padding: 4px 8px;
        }
        
        .add-user-form {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .password-generator {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>
    
    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Main Content Area -->
            <main class="col-12 px-md-4 main-content-with-right-sidebar">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-users me-2"></i>
                        إدارة المستخدمين
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                                <i class="fas fa-user-plus"></i> إضافة مستخدم جديد
                            </button>
                        </div>
                    </div>
                </div>

                <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-users fa-2x text-primary mb-2"></i>
                                <h4><?php echo count($users); ?></h4>
                                <small class="text-muted">إجمالي المستخدمين</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-user-check fa-2x text-success mb-2"></i>
                                <h4><?php echo count(array_filter($users, function($u) { return $u['is_active']; })); ?></h4>
                                <small class="text-muted">المستخدمين النشطين</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-user-tie fa-2x text-info mb-2"></i>
                                <h4><?php echo count(array_filter($users, function($u) { return in_array($u['user_type'], ['branch_manager', 'financial_manager', 'department_head']); })); ?></h4>
                                <small class="text-muted">المدراء والرؤساء</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-building fa-2x text-warning mb-2"></i>
                                <h4><?php echo count($branches); ?></h4>
                                <small class="text-muted">المراكز المتاحة</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قائمة المستخدمين -->
                <div class="row">
                    <?php foreach ($users as $userItem): ?>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card user-card position-relative">
                            <div class="status-badge">
                                <?php if ($userItem['is_active']): ?>
                                <span class="badge bg-success">نشط</span>
                                <?php else: ?>
                                <span class="badge bg-secondary">غير نشط</span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="card-body text-center">
                                <div class="user-avatar mx-auto mb-3">
                                    <?php echo strtoupper(mb_substr($userItem['full_name'], 0, 1)); ?>
                                </div>
                                
                                <h5 class="card-title"><?php echo htmlspecialchars($userItem['full_name']); ?></h5>
                                <p class="text-muted mb-2">@<?php echo htmlspecialchars($userItem['username']); ?></p>
                                
                                <span class="badge user-type-badge bg-<?php 
                                    echo match($userItem['user_type']) {
                                        'admin' => 'danger',
                                        'main_accountant' => 'warning',
                                        'branch_manager' => 'primary',
                                        'financial_manager' => 'info',
                                        'department_head' => 'success',
                                        default => 'secondary'
                                    };
                                ?>">
                                    <?php echo $userTypes[$userItem['user_type']] ?? $userItem['user_type']; ?>
                                </span>
                                
                                <?php if ($userItem['branch_name']): ?>
                                <p class="mt-2 mb-1"><small><i class="fas fa-building me-1"></i><?php echo htmlspecialchars($userItem['branch_name']); ?></small></p>
                                <?php endif; ?>
                                
                                <?php if ($userItem['department']): ?>
                                <p class="mb-1"><small><i class="fas fa-sitemap me-1"></i><?php echo htmlspecialchars($userItem['department']); ?></small></p>
                                <?php endif; ?>
                                
                                <?php if ($userItem['email']): ?>
                                <p class="mb-1"><small><i class="fas fa-envelope me-1"></i><?php echo htmlspecialchars($userItem['email']); ?></small></p>
                                <?php endif; ?>
                                
                                <?php if ($userItem['phone']): ?>
                                <p class="mb-3"><small><i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($userItem['phone']); ?></small></p>
                                <?php endif; ?>
                                
                                <div class="btn-group w-100">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="showUserDetails(<?php echo $userItem['id']; ?>)">
                                        <i class="fas fa-eye"></i> عرض
                                    </button>
                                    
                                    <?php if ($userItem['id'] != $_SESSION['user_id']): ?>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="toggle_status">
                                        <input type="hidden" name="user_id" value="<?php echo $userItem['id']; ?>">
                                        <input type="hidden" name="current_status" value="<?php echo $userItem['is_active']; ?>">
                                        <button type="submit" class="btn btn-outline-<?php echo $userItem['is_active'] ? 'warning' : 'success'; ?> btn-sm"
                                                onclick="return confirm('هل أنت متأكد من تغيير حالة هذا المستخدم؟')">
                                            <i class="fas fa-<?php echo $userItem['is_active'] ? 'pause' : 'play'; ?>"></i>
                                            <?php echo $userItem['is_active'] ? 'إيقاف' : 'تفعيل'; ?>
                                        </button>
                                    </form>
                                    <?php endif; ?>
                                </div>
                                
                                <small class="text-muted d-block mt-2">
                                    انضم في: <?php echo date('Y-m-d', strtotime($userItem['created_at'])); ?>
                                </small>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </main>
            
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
        </div>
    </div>

    <!-- Modal إضافة مستخدم -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-plus me-2"></i>
                        إضافة مستخدم جديد
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                
                <form method="POST">
                    <input type="hidden" name="action" value="add_user">
                    
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="username" name="username" required>
                                    <div class="form-text">سيستخدم للدخول إلى النظام</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="password" name="password" required minlength="6">
                                        <button type="button" class="btn btn-outline-secondary" onclick="togglePassword()">
                                            <i class="fas fa-eye" id="passwordIcon"></i>
                                        </button>
                                    </div>
                                    <div class="password-generator">
                                        <button type="button" class="btn btn-sm btn-info" onclick="generatePassword()">
                                            <i class="fas fa-random me-1"></i>
                                            توليد كلمة مرور قوية
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="full_name" name="full_name" required>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="email" name="email">
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" id="phone" name="phone">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="user_type" class="form-label">نوع المستخدم <span class="text-danger">*</span></label>
                                    <select class="form-select" id="user_type" name="user_type" required>
                                        <option value="">اختر نوع المستخدم</option>
                                        <?php foreach ($userTypes as $type => $label): ?>
                                        <option value="<?php echo $type; ?>"><?php echo $label; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="branch_id" class="form-label">المركز</label>
                                    <select class="form-select" id="branch_id" name="branch_id">
                                        <option value="">اختر المركز (اختياري)</option>
                                        <?php foreach ($branches as $branch): ?>
                                        <option value="<?php echo $branch['id']; ?>">
                                            <?php echo htmlspecialchars($branch['name']) . ' (' . htmlspecialchars($branch['code']) . ')'; ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="department" class="form-label">القسم/الإدارة</label>
                            <input type="text" class="form-control" id="department" name="department" 
                                   placeholder="مثال: قسم المحاسبة، إدارة الخزينة، قسم المشتريات">
                        </div>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ المستخدم
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // توليد كلمة مرور قوية
        function generatePassword() {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789@#$%';
            let password = '';
            for (let i = 0; i < 8; i++) {
                password += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            document.getElementById('password').value = password;
        }
        
        // إظهار/إخفاء كلمة المرور
        function togglePassword() {
            const passwordField = document.getElementById('password');
            const passwordIcon = document.getElementById('passwordIcon');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                passwordIcon.className = 'fas fa-eye-slash';
            } else {
                passwordField.type = 'password';
                passwordIcon.className = 'fas fa-eye';
            }
        }
        
        // عرض تفاصيل المستخدم
        function showUserDetails(userId) {
            // يمكن إضافة modal لعرض تفاصيل المستخدم
            alert('عرض تفاصيل المستخدم ID: ' + userId);
        }
        
        // تحديث اسم المستخدم تلقائياً من الاسم الكامل
        document.getElementById('full_name').addEventListener('input', function() {
            const fullName = this.value.trim();
            const usernameField = document.getElementById('username');
            
            if (fullName && !usernameField.value) {
                // إنشاء اسم مستخدم من الاسم الكامل
                const names = fullName.split(' ');
                let username = '';
                
                if (names.length >= 2) {
                    username = names[0].toLowerCase() + '.' + names[1].toLowerCase();
                } else {
                    username = names[0].toLowerCase();
                }
                
                // إزالة الأحرف غير المسموحة
                username = username.replace(/[^a-z0-9.]/g, '');
                usernameField.value = username;
            }
        });
    </script>
</body>
</html>
