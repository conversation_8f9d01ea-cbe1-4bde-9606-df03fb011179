<?php
// ملف تثبيت نظام المحاسبة المالية
error_reporting(E_ALL);
ini_set('display_errors', 1);

$step = $_GET['step'] ?? 1;
$error = '';
$success = '';

// التحقق من متطلبات النظام
function checkRequirements() {
    $requirements = [
        'PHP Version >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
        'PDO Extension' => extension_loaded('pdo'),
        'PDO MySQL Extension' => extension_loaded('pdo_mysql'),
        'JSON Extension' => extension_loaded('json'),
        'MBString Extension' => extension_loaded('mbstring'),
        'OpenSSL Extension' => extension_loaded('openssl'),
        'Writable uploads directory' => is_writable('uploads') || mkdir('uploads', 0755, true),
        'Writable backups directory' => is_writable('backups') || mkdir('backups', 0755, true),
    ];

    return $requirements;
}

// إنشاء قاعدة البيانات والجداول
function createDatabase($host, $username, $password, $database) {
    try {
        // الاتصال بـ MySQL بدون تحديد قاعدة البيانات
        $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // إنشاء قاعدة البيانات
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `$database`");

        // قراءة وتنفيذ ملف SQL
        $sql = file_get_contents('database/schema.sql');

        // تقسيم الاستعلامات
        $statements = array_filter(array_map('trim', explode(';', $sql)));

        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^(--|\/\*|\*)/', $statement)) {
                $pdo->exec($statement);
            }
        }

        return true;
    } catch (PDOException $e) {
        throw new Exception("خطأ في إنشاء قاعدة البيانات: " . $e->getMessage());
    }
}

// إنشاء ملف الإعدادات
function createConfigFile($host, $username, $password, $database) {
    $configContent = "<?php
// إعدادات قاعدة البيانات
define('DB_HOST', '$host');
define('DB_NAME', '$database');
define('DB_USER', '$username');
define('DB_PASS', '$password');
define('DB_CHARSET', 'utf8mb4');

// إنشاء اتصال قاعدة البيانات
try {
    \$pdo = new PDO(
        \"mysql:host=\" . DB_HOST . \";dbname=\" . DB_NAME . \";charset=\" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    );
} catch (PDOException \$e) {
    die(\"خطأ في الاتصال بقاعدة البيانات: \" . \$e->getMessage());
}

// إعدادات النظام
define('SYSTEM_NAME', 'نظام المحاسبة المالية');
define('SYSTEM_VERSION', '1.0.0');
define('CURRENCY', 'دينار أردني');
define('CURRENCY_SYMBOL', 'د.أ');
define('DATE_FORMAT', 'Y-m-d');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');

// إعدادات الأمان
define('SESSION_TIMEOUT', 3600);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900);

// إعدادات الملفات
define('UPLOAD_PATH', 'uploads/');
define('BACKUP_PATH', 'backups/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024);

// أنواع المستخدمين
define('USER_TYPE_ADMIN', 'admin');
define('USER_TYPE_MAIN_ACCOUNTANT', 'main_accountant');
define('USER_TYPE_BRANCH_ACCOUNTANT', 'branch_accountant');
define('USER_TYPE_CASHIER', 'cashier');

// حالات الفواتير
define('INVOICE_STATUS_DRAFT', 'draft');
define('INVOICE_STATUS_PENDING', 'pending');
define('INVOICE_STATUS_APPROVED', 'approved');
define('INVOICE_STATUS_PAID', 'paid');
define('INVOICE_STATUS_CANCELLED', 'cancelled');

// أنواع الحركات المالية
define('TRANSACTION_TYPE_SALE', 'sale');
define('TRANSACTION_TYPE_PURCHASE', 'purchase');
define('TRANSACTION_TYPE_EXPENSE', 'expense');
define('TRANSACTION_TYPE_INCOME', 'income');
define('TRANSACTION_TYPE_TRANSFER', 'transfer');

// أنواع نقاط البيع
define('POS_TYPE_SUPERMARKET', 'supermarket');
define('POS_TYPE_CAFETERIA', 'cafeteria');
define('POS_TYPE_NUTS_SHOP', 'nuts_shop');
define('POS_TYPE_SWEETS', 'sweets');
define('POS_TYPE_BAKERY', 'bakery');

// إعدادات التقارير
define('REPORT_ITEMS_PER_PAGE', 50);
define('CHART_COLORS', [
    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
    '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'
]);

// رسائل النظام
define('MSG_SUCCESS', 'success');
define('MSG_ERROR', 'error');
define('MSG_WARNING', 'warning');
define('MSG_INFO', 'info');

// وظائف مساعدة لقاعدة البيانات
function getConnection() {
    global \$pdo;
    return \$pdo;
}

function executeQuery(\$sql, \$params = []) {
    try {
        \$pdo = getConnection();
        \$stmt = \$pdo->prepare(\$sql);
        \$stmt->execute(\$params);
        return \$stmt;
    } catch (PDOException \$e) {
        error_log(\"Database Error: \" . \$e->getMessage());
        throw new Exception(\"حدث خطأ في قاعدة البيانات\");
    }
}

function fetchOne(\$sql, \$params = []) {
    \$stmt = executeQuery(\$sql, \$params);
    return \$stmt->fetch();
}

function fetchAll(\$sql, \$params = []) {
    \$stmt = executeQuery(\$sql, \$params);
    return \$stmt->fetchAll();
}

function getLastInsertId() {
    \$pdo = getConnection();
    return \$pdo->lastInsertId();
}

function beginTransaction() {
    \$pdo = getConnection();
    return \$pdo->beginTransaction();
}

function commit() {
    \$pdo = getConnection();
    return \$pdo->commit();
}

function rollback() {
    \$pdo = getConnection();
    return \$pdo->rollback();
}
?>";

    return file_put_contents('config/database.php', $configContent);
}

// معالجة النماذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step == 2) {
        $host = $_POST['host'] ?? 'localhost';
        $username = $_POST['username'] ?? '';
        $password = $_POST['password'] ?? '';
        $database = $_POST['database'] ?? '';

        if (empty($username) || empty($database)) {
            $error = 'يرجى ملء جميع الحقول المطلوبة';
        } else {
            try {
                createDatabase($host, $username, $password, $database);
                createConfigFile($host, $username, $password, $database);
                $success = 'تم إنشاء قاعدة البيانات بنجاح!';
                $step = 3;
            } catch (Exception $e) {
                $error = $e->getMessage();
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام المحاسبة المالية</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .install-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin: 50px auto;
            max-width: 800px;
            overflow: hidden;
        }

        .install-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .install-body {
            padding: 40px;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
            position: relative;
        }

        .step.active {
            background: #007bff;
            color: white;
        }

        .step.completed {
            background: #28a745;
            color: white;
        }

        .step:not(:last-child)::after {
            content: '';
            position: absolute;
            left: -20px;
            width: 20px;
            height: 2px;
            background: #e9ecef;
            top: 50%;
            transform: translateY(-50%);
        }

        .requirement {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .requirement:last-child {
            border-bottom: none;
        }

        .requirement-status {
            font-weight: bold;
        }

        .requirement-status.pass {
            color: #28a745;
        }

        .requirement-status.fail {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="install-container">
            <div class="install-header">
                <h1><i class="fas fa-calculator me-3"></i>نظام المحاسبة المالية</h1>
                <p class="mb-0">معالج التثبيت والإعداد</p>
            </div>

            <div class="install-body">
                <!-- مؤشر الخطوات -->
                <div class="step-indicator">
                    <div class="step <?php echo $step >= 1 ? ($step > 1 ? 'completed' : 'active') : ''; ?>">1</div>
                    <div class="step <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : ''; ?>">2</div>
                    <div class="step <?php echo $step >= 3 ? 'active' : ''; ?>">3</div>
                </div>

                <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
                <?php endif; ?>

                <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
                <?php endif; ?>

                <?php if ($step == 1): ?>
                <!-- الخطوة 1: فحص المتطلبات -->
                <h3><i class="fas fa-check-circle me-2"></i>فحص متطلبات النظام</h3>
                <p class="text-muted mb-4">يرجى التأكد من توفر جميع المتطلبات التالية:</p>

                <?php
                $requirements = checkRequirements();
                $allPassed = true;
                ?>

                <div class="requirements-list">
                    <?php foreach ($requirements as $requirement => $status): ?>
                    <div class="requirement">
                        <span><?php echo $requirement; ?></span>
                        <span class="requirement-status <?php echo $status ? 'pass' : 'fail'; ?>">
                            <?php if ($status): ?>
                                <i class="fas fa-check"></i> متوفر
                            <?php else: ?>
                                <i class="fas fa-times"></i> غير متوفر
                                <?php $allPassed = false; ?>
                            <?php endif; ?>
                        </span>
                    </div>
                    <?php endforeach; ?>
                </div>

                <div class="text-center mt-4">
                    <?php if ($allPassed): ?>
                    <a href="?step=2" class="btn btn-primary btn-lg">
                        <i class="fas fa-arrow-left me-2"></i>
                        المتابعة إلى الخطوة التالية
                    </a>
                    <?php else: ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        يرجى حل المشاكل المذكورة أعلاه قبل المتابعة
                    </div>
                    <button onclick="location.reload()" class="btn btn-secondary">
                        <i class="fas fa-sync-alt me-2"></i>
                        إعادة الفحص
                    </button>
                    <?php endif; ?>
                </div>

                <?php elseif ($step == 2): ?>
                <!-- الخطوة 2: إعداد قاعدة البيانات -->
                <h3><i class="fas fa-database me-2"></i>إعداد قاعدة البيانات</h3>
                <p class="text-muted mb-4">يرجى إدخال معلومات الاتصال بقاعدة البيانات:</p>

                <form method="POST">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="host" class="form-label">خادم قاعدة البيانات</label>
                                <input type="text" class="form-control" id="host" name="host"
                                       value="localhost" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="database" class="form-label">اسم قاعدة البيانات</label>
                                <input type="text" class="form-control" id="database" name="database"
                                       value="schema" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">اسم المستخدم</label>
                                <input type="text" class="form-control" id="username" name="username"
                                       value="md" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" id="password" name="password"
                                       value="mm$$12345dd">
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> سيتم إنشاء قاعدة البيانات تلقائياً إذا لم تكن موجودة.
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-database me-2"></i>
                            إنشاء قاعدة البيانات
                        </button>
                    </div>
                </form>

                <?php elseif ($step == 3): ?>
                <!-- الخطوة 3: اكتمال التثبيت -->
                <div class="text-center">
                    <div class="mb-4">
                        <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                    </div>

                    <h3 class="text-success mb-3">تم التثبيت بنجاح!</h3>
                    <p class="text-muted mb-4">تم إعداد نظام المحاسبة المالية بنجاح. يمكنك الآن البدء في استخدام النظام.</p>

                    <div class="alert alert-info text-start">
                        <h5><i class="fas fa-user-shield me-2"></i>بيانات تسجيل الدخول الافتراضية:</h5>
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>مدير النظام:</strong><br>
                                اسم المستخدم: <code>admin</code><br>
                                كلمة المرور: <code>password</code>
                            </div>
                            <div class="col-md-6">
                                <strong>المحاسب الرئيسي:</strong><br>
                                اسم المستخدم: <code>main_accountant</code><br>
                                كلمة المرور: <code>password</code>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>مهم:</strong> يرجى تغيير كلمات المرور الافتراضية فور تسجيل الدخول لأول مرة.
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                        <a href="login.php" class="btn btn-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            تسجيل الدخول
                        </a>
                        <button onclick="deleteInstaller()" class="btn btn-danger btn-lg">
                            <i class="fas fa-trash me-2"></i>
                            حذف ملف التثبيت
                        </button>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function deleteInstaller() {
            if (confirm('هل أنت متأكد من حذف ملف التثبيت؟ لن تتمكن من استعادته.')) {
                fetch('install.php?action=delete', {
                    method: 'POST'
                })
                .then(response => response.text())
                .then(data => {
                    alert('تم حذف ملف التثبيت بنجاح');
                    window.location.href = 'login.php';
                })
                .catch(error => {
                    alert('حدث خطأ في حذف الملف');
                });
            }
        }
    </script>
</body>
</html>

<?php
// حذف ملف التثبيت
if (isset($_GET['action']) && $_GET['action'] === 'delete' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    unlink(__FILE__);
    echo 'تم الحذف';
    exit;
}
?>
