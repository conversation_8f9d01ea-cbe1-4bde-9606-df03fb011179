-- نظام المحاسبة المالية - هيكل قاعدة البيانات
-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS schema CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE schema;

-- جدول الشركات/المؤسسات
CREATE TABLE companies (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    commercial_register VARCHAR(100),
    tax_number VARCHAR(100),
    address TEXT,
    phone VARCHAR(50),
    email VARCHAR(100),
    logo VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الفروع
CREATE TABLE branches (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    address TEXT,
    phone VARCHAR(50),
    manager_name VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE
);

-- جدول نقاط البيع
CREATE TABLE point_of_sales (
    id INT PRIMARY KEY AUTO_INCREMENT,
    branch_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    type ENUM('supermarket', 'cafeteria', 'nuts_shop', 'sweets', 'bakery') NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE
);

-- جدول المستخدمين
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(50),
    user_type ENUM('admin', 'main_accountant', 'branch_accountant', 'cashier') NOT NULL,
    branch_id INT,
    pos_id INT,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE SET NULL,
    FOREIGN KEY (pos_id) REFERENCES point_of_sales(id) ON DELETE SET NULL
);

-- جدول صلاحيات المستخدمين
CREATE TABLE user_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    module VARCHAR(100) NOT NULL,
    permission_type ENUM('view', 'create', 'edit', 'delete', 'approve') NOT NULL,
    granted BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_module_permission (user_id, module, permission_type)
);

-- جدول العملاء
CREATE TABLE customers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    company_name VARCHAR(255),
    tax_number VARCHAR(100),
    phone VARCHAR(50),
    email VARCHAR(100),
    address TEXT,
    credit_limit DECIMAL(15,2) DEFAULT 0,
    current_balance DECIMAL(15,2) DEFAULT 0,
    customer_type ENUM('individual', 'company') DEFAULT 'individual',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الموردين
CREATE TABLE suppliers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    company_name VARCHAR(255),
    tax_number VARCHAR(100),
    phone VARCHAR(50),
    email VARCHAR(100),
    address TEXT,
    current_balance DECIMAL(15,2) DEFAULT 0,
    supplier_type ENUM('local', 'international') DEFAULT 'local',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول فئات المنتجات
CREATE TABLE product_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    parent_id INT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES product_categories(id) ON DELETE SET NULL
);

-- جدول المنتجات
CREATE TABLE products (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(100) UNIQUE NOT NULL,
    barcode VARCHAR(100),
    category_id INT,
    description TEXT,
    unit VARCHAR(50) NOT NULL,
    cost_price DECIMAL(10,2) NOT NULL,
    selling_price DECIMAL(10,2) NOT NULL,
    min_stock_level INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES product_categories(id) ON DELETE SET NULL
);

-- جدول المخزون
CREATE TABLE inventory (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_id INT NOT NULL,
    branch_id INT NOT NULL,
    pos_id INT,
    current_stock INT NOT NULL DEFAULT 0,
    reserved_stock INT NOT NULL DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE,
    FOREIGN KEY (pos_id) REFERENCES point_of_sales(id) ON DELETE SET NULL,
    UNIQUE KEY unique_product_branch_pos (product_id, branch_id, pos_id)
);

-- جدول الفواتير
CREATE TABLE invoices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_number VARCHAR(100) UNIQUE NOT NULL,
    invoice_type ENUM('sale', 'purchase', 'return_sale', 'return_purchase') NOT NULL,
    branch_id INT NOT NULL,
    pos_id INT,
    customer_id INT,
    supplier_id INT,
    user_id INT NOT NULL,
    invoice_date DATE NOT NULL,
    due_date DATE,
    subtotal DECIMAL(15,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    discount_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    total_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    paid_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    remaining_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    status ENUM('draft', 'pending', 'approved', 'paid', 'cancelled') DEFAULT 'draft',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE,
    FOREIGN KEY (pos_id) REFERENCES point_of_sales(id) ON DELETE SET NULL,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT
);

-- جدول تفاصيل الفواتير
CREATE TABLE invoice_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity DECIMAL(10,2) NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    discount_percentage DECIMAL(5,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    tax_percentage DECIMAL(5,2) DEFAULT 0,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(15,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT
);

-- جدول المدفوعات
CREATE TABLE payments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_id INT NOT NULL,
    payment_method ENUM('cash', 'bank_transfer', 'check', 'credit_card') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    payment_date DATE NOT NULL,
    reference_number VARCHAR(100),
    notes TEXT,
    user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT
);

-- جدول الحسابات المالية
CREATE TABLE accounts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    account_code VARCHAR(50) UNIQUE NOT NULL,
    account_name VARCHAR(255) NOT NULL,
    account_type ENUM('asset', 'liability', 'equity', 'revenue', 'expense') NOT NULL,
    parent_id INT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES accounts(id) ON DELETE SET NULL
);

-- جدول القيود المحاسبية
CREATE TABLE journal_entries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    entry_number VARCHAR(100) UNIQUE NOT NULL,
    entry_date DATE NOT NULL,
    description TEXT NOT NULL,
    reference_type ENUM('invoice', 'payment', 'adjustment', 'opening_balance') NOT NULL,
    reference_id INT,
    branch_id INT NOT NULL,
    user_id INT NOT NULL,
    total_debit DECIMAL(15,2) NOT NULL DEFAULT 0,
    total_credit DECIMAL(15,2) NOT NULL DEFAULT 0,
    status ENUM('draft', 'posted', 'cancelled') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT
);

-- جدول تفاصيل القيود المحاسبية
CREATE TABLE journal_entry_details (
    id INT PRIMARY KEY AUTO_INCREMENT,
    journal_entry_id INT NOT NULL,
    account_id INT NOT NULL,
    debit_amount DECIMAL(15,2) DEFAULT 0,
    credit_amount DECIMAL(15,2) DEFAULT 0,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE RESTRICT
);

-- جدول المصروفات
CREATE TABLE expenses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    expense_number VARCHAR(100) UNIQUE NOT NULL,
    branch_id INT NOT NULL,
    pos_id INT,
    expense_type VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    expense_date DATE NOT NULL,
    user_id INT NOT NULL,
    approved_by INT,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    receipt_image VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE,
    FOREIGN KEY (pos_id) REFERENCES point_of_sales(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT,
    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL
);

-- جدول سجل الأنشطة
CREATE TABLE activity_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    action VARCHAR(255) NOT NULL,
    description TEXT,
    table_name VARCHAR(100),
    record_id INT,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول النسخ الاحتياطية
CREATE TABLE backups (
    id INT PRIMARY KEY AUTO_INCREMENT,
    filename VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    backup_type ENUM('manual', 'automatic') NOT NULL,
    description TEXT,
    user_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- جدول الإعدادات
CREATE TABLE settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    updated_by INT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
);

-- إدراج البيانات الأساسية

-- إدراج شركة افتراضية
INSERT INTO companies (name, commercial_register, tax_number, address, phone, email) VALUES
('الشركة التجارية المتحدة', '*********', '*********', 'عمان - الأردن', '+962-6-1234567', '<EMAIL>');

-- إدراج الفروع
INSERT INTO branches (company_id, name, code, address, phone, manager_name) VALUES
(1, 'فرع الزرقاء', 'ZRQ001', 'الزرقاء - شارع الملك عبدالله', '+962-5-1234567', 'أحمد محمد'),
(1, 'فرع ماركا', 'MRK001', 'ماركا - شارع الجامعة', '+962-6-2345678', 'محمد أحمد'),
(1, 'فرع قفقفا', 'QFQ001', 'قفقفا - الشارع الرئيسي', '+962-6-3456789', 'سارة علي');

-- إدراج نقاط البيع
INSERT INTO point_of_sales (branch_id, name, type, description) VALUES
(1, 'سوبر ماركت الزرقاء', 'supermarket', 'سوبر ماركت شامل'),
(1, 'كافتيريا الزرقاء', 'cafeteria', 'كافتيريا ومشروبات'),
(1, 'محمص الزرقاء', 'nuts_shop', 'بزر ومكسرات'),
(1, 'حلويات الزرقاء', 'sweets', 'حلويات شرقية وغربية'),
(1, 'مخبز الزرقاء', 'bakery', 'خبز ومعجنات'),
(2, 'سوبر ماركت ماركا', 'supermarket', 'سوبر ماركت شامل'),
(2, 'كافتيريا ماركا', 'cafeteria', 'كافتيريا ومشروبات'),
(3, 'سوبر ماركت قفقفا', 'supermarket', 'سوبر ماركت شامل');

-- إدراج المستخدمين الافتراضيين (كلمة المرور: password)
INSERT INTO users (username, password, full_name, email, user_type, branch_id) VALUES
('admin', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'مدير النظام', '<EMAIL>', 'admin', NULL),
('main_accountant', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'المحاسب الرئيسي', '<EMAIL>', 'main_accountant', NULL),
('zarqa_accountant', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'محاسب فرع الزرقاء', '<EMAIL>', 'branch_accountant', 1),
('marka_accountant', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'محاسب فرع ماركا', '<EMAIL>', 'branch_accountant', 2);

-- إدراج فئات المنتجات
INSERT INTO product_categories (name, description) VALUES
('مواد غذائية', 'جميع المواد الغذائية'),
('مشروبات', 'المشروبات الباردة والساخنة'),
('منظفات', 'مواد التنظيف والعناية'),
('مكسرات وبزر', 'جميع أنواع المكسرات والبزر'),
('حلويات', 'الحلويات الشرقية والغربية'),
('مخبوزات', 'الخبز والمعجنات');

-- إدراج منتجات تجريبية
INSERT INTO products (name, code, barcode, category_id, unit, cost_price, selling_price, min_stock_level) VALUES
('أرز بسمتي 5 كيلو', 'RICE001', '*********0123', 1, 'كيس', 15.00, 18.00, 10),
('زيت زيتون 1 لتر', 'OIL001', '*********0124', 1, 'زجاجة', 8.00, 12.00, 5),
('كوكا كولا 330 مل', 'COKE001', '*********0125', 2, 'علبة', 0.30, 0.50, 50),
('مسحوق غسيل 3 كيلو', 'DETERG001', '*********0126', 3, 'علبة', 5.00, 7.50, 8),
('لوز محمص 500 جرام', 'ALMOND001', '*********0127', 4, 'كيس', 12.00, 15.00, 15),
('كنافة نابلسية', 'KNAFEH001', '*********0128', 5, 'كيلو', 8.00, 12.00, 5),
('خبز عربي', 'BREAD001', '*********0129', 6, 'ربطة', 0.20, 0.30, 100);

-- إدراج الحسابات المحاسبية الأساسية
INSERT INTO accounts (account_code, account_name, account_type) VALUES
('1000', 'الأصول', 'asset'),
('1100', 'الأصول المتداولة', 'asset'),
('1110', 'النقدية', 'asset'),
('1120', 'المخزون', 'asset'),
('1130', 'الذمم المدينة', 'asset'),
('2000', 'الخصوم', 'liability'),
('2100', 'الخصوم المتداولة', 'liability'),
('2110', 'الذمم الدائنة', 'liability'),
('3000', 'حقوق الملكية', 'equity'),
('3100', 'رأس المال', 'equity'),
('4000', 'الإيرادات', 'revenue'),
('4100', 'إيرادات المبيعات', 'revenue'),
('5000', 'المصروفات', 'expense'),
('5100', 'تكلفة البضاعة المباعة', 'expense'),
('5200', 'مصروفات التشغيل', 'expense');

-- إدراج الإعدادات الافتراضية
INSERT INTO settings (setting_key, setting_value, description) VALUES
('company_name', 'الشركة التجارية المتحدة', 'اسم الشركة'),
('tax_rate', '16', 'معدل الضريبة %'),
('currency', 'دينار أردني', 'العملة المستخدمة'),
('currency_symbol', 'د.أ', 'رمز العملة'),
('backup_frequency', 'daily', 'تكرار النسخ الاحتياطي'),
('session_timeout', '3600', 'مهلة انتهاء الجلسة بالثواني'),
('invoice_prefix_sale', 'SAL', 'بادئة فواتير المبيعات'),
('invoice_prefix_purchase', 'PUR', 'بادئة فواتير المشتريات');

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX idx_invoices_date ON invoices(invoice_date);
CREATE INDEX idx_invoices_branch ON invoices(branch_id);
CREATE INDEX idx_invoices_customer ON invoices(customer_id);
CREATE INDEX idx_invoices_supplier ON invoices(supplier_id);
CREATE INDEX idx_inventory_product ON inventory(product_id);
CREATE INDEX idx_inventory_branch ON inventory(branch_id);
CREATE INDEX idx_activity_logs_user ON activity_logs(user_id);
CREATE INDEX idx_activity_logs_date ON activity_logs(created_at);
CREATE INDEX idx_journal_entries_date ON journal_entries(entry_date);
CREATE INDEX idx_journal_entries_branch ON journal_entries(branch_id);
