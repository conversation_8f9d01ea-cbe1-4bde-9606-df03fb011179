# نظام المحاسبة المالية المتكامل

## 🏢 نظرة عامة

نظام محاسبة مالية احترافي ومتكامل مصمم خصيصاً للشركات والمؤسسات التي تدير عدة فروع ونقاط بيع. يوفر النظام إدارة شاملة للعمليات المحاسبية مع واجهة عربية حديثة ومتجاوبة.

## ✨ المميزات الرئيسية

### 🏪 إدارة متعددة الفروع
- إدارة عدد غير محدود من الفروع
- نقاط بيع متنوعة (سوبر ماركت، كافتيريا، محمص، حلويات، مخبز)
- ربحية مستقلة لكل نقطة بيع
- تقارير مقارنة بين الفروع

### 👥 نظام صلاحيات متقدم
- **مدير النظام**: صلاحيات كاملة على جميع الوحدات
- **المحاسب الرئيسي**: إشراف على جميع الفروع والتقارير المالية
- **محاسب الفرع**: إدارة العمليات المحاسبية للفرع المخصص
- **الكاشير**: إدخال المبيعات والعمليات الأساسية

### 📊 الفواتير والمبيعات
- فواتير مبيعات ومشتريات احترافية
- دعم العملاء النقديين والآجلين
- إدارة المدفوعات والأقساط
- فواتير الإرجاع والاستبدال
- طباعة فواتير حرارية

### 📈 التقارير المالية الشاملة
- **الميزانية العمومية**: عرض الأصول والخصوم وحقوق الملكية
- **قائمة الدخل**: الإيرادات والمصروفات والأرباح
- **تقارير المبيعات**: تفصيلية حسب الفترة والفرع والمنتج
- **تقارير المشتريات**: متابعة المشتريات والموردين
- **تقارير المخزون**: حالة المخزون وحركة البضائع
- **تقارير العملاء**: الذمم المدينة والدائنة

### 🏦 المحاسبة المتقدمة
- دليل حسابات مرن وقابل للتخصيص
- القيود اليومية التلقائية والمدخلة يدوياً
- ميزان المراجعة
- إدارة المصروفات والإيرادات
- تتبع الذمم المالية

### 📦 إدارة المخزون
- تصنيف المنتجات بفئات متعددة المستويات
- تتبع حركة المخزون في الوقت الفعلي
- تنبيهات المخزون المنخفض
- جرد دوري وسنوي
- دعم الباركود

### 🔒 الأمان والحماية
- تشفير كلمات المرور
- تسجيل جميع العمليات والأنشطة
- جلسات آمنة مع انتهاء صلاحية
- نسخ احتياطية تلقائية ويدوية
- حماية من محاولات الاختراق

## 🛠️ المتطلبات التقنية

### متطلبات الخادم
- **PHP**: الإصدار 7.4 أو أحدث
- **MySQL**: الإصدار 5.7 أو أحدث (يُفضل 8.0)
- **Apache/Nginx**: خادم ويب
- **الذاكرة**: 512 ميجابايت كحد أدنى (يُفضل 1 جيجابايت)
- **مساحة القرص**: 1 جيجابايت كحد أدنى

### الإضافات المطلوبة لـ PHP
- PDO
- PDO MySQL
- JSON
- MBString
- OpenSSL
- GD (للرسوم البيانية)
- Zip (للنسخ الاحتياطية)

### متطلبات المتصفح
- **Chrome**: الإصدار 80 أو أحدث
- **Firefox**: الإصدار 75 أو أحدث
- **Safari**: الإصدار 13 أو أحدث
- **Edge**: الإصدار 80 أو أحدث

## 📥 التثبيت والإعداد

### 1. تحميل الملفات
```bash
# استنساخ المشروع
git clone https://github.com/your-repo/accounting-system.git

# أو تحميل الملفات مباشرة إلى مجلد htdocs في XAMPP
```

### 2. إعداد قاعدة البيانات
1. تشغيل XAMPP وتفعيل Apache و MySQL
2. فتح المتصفح والذهاب إلى: `http://localhost/accounting-system/install.php`
3. اتباع خطوات معالج التثبيت

### 3. إعداد الصلاحيات
```bash
# إعطاء صلاحيات الكتابة للمجلدات
chmod 755 uploads/
chmod 755 backups/
chmod 644 config/database.php
```

### 4. بيانات الدخول الافتراضية

#### مدير النظام
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `password`

#### المحاسب الرئيسي
- **اسم المستخدم**: `main_accountant`
- **كلمة المرور**: `password`

#### محاسب فرع الزرقاء
- **اسم المستخدم**: `zarqa_accountant`
- **كلمة المرور**: `password`

> ⚠️ **تحذير**: يجب تغيير كلمات المرور الافتراضية فور تسجيل الدخول لأول مرة.

## 🚀 الاستخدام السريع

### إضافة فرع جديد
1. تسجيل الدخول كمدير نظام
2. الذهاب إلى "إدارة الفروع" → "إضافة فرع"
3. ملء بيانات الفرع وحفظ

### إنشاء فاتورة مبيعات
1. الذهاب إلى "الفواتير" → "فاتورة مبيعات جديدة"
2. اختيار العميل والمنتجات
3. حفظ وطباعة الفاتورة

### عرض التقارير
1. الذهاب إلى "التقارير"
2. اختيار نوع التقرير المطلوب
3. تحديد الفترة الزمنية والفلاتر
4. عرض أو تصدير التقرير

## 📁 هيكل المشروع

```
accounting-system/
├── assets/                 # الملفات الثابتة (CSS, JS, Images)
│   ├── css/
│   ├── js/
│   └── images/
├── config/                 # ملفات الإعدادات
│   └── database.php
├── database/               # ملفات قاعدة البيانات
│   └── schema.sql
├── includes/               # الملفات المشتركة
│   ├── functions.php
│   ├── navbar.php
│   └── sidebar.php
├── modules/                # وحدات النظام
│   ├── invoices/          # إدارة الفواتير
│   ├── customers/         # إدارة العملاء
│   ├── suppliers/         # إدارة الموردين
│   ├── inventory/         # إدارة المخزون
│   ├── reports/           # التقارير
│   ├── accounting/        # المحاسبة
│   └── admin/             # إدارة النظام
├── uploads/               # الملفات المرفوعة
├── backups/               # النسخ الاحتياطية
├── api/                   # واجهات برمجة التطبيقات
├── index.php              # الصفحة الرئيسية
├── login.php              # صفحة تسجيل الدخول
├── logout.php             # تسجيل الخروج
├── install.php            # معالج التثبيت
└── README.md              # هذا الملف
```

## 🔧 الإعدادات المتقدمة

### إعداد النسخ الاحتياطية التلقائية
```php
// في ملف cron job أو مهمة مجدولة
php /path/to/accounting-system/scripts/backup.php
```

### إعداد الإشعارات
```php
// تخصيص إعدادات الإشعارات في
config/notifications.php
```

### إعداد الطباعة
```javascript
// تخصيص إعدادات الطباعة في
assets/js/print-config.js
```

## 🎨 التخصيص

### تغيير الألوان والتصميم
```css
/* في ملف assets/css/style.css */
:root {
    --primary-color: #007bff;    /* اللون الأساسي */
    --secondary-color: #6c757d;  /* اللون الثانوي */
    --success-color: #28a745;    /* لون النجاح */
    --danger-color: #dc3545;     /* لون الخطر */
}
```

### إضافة حقول مخصصة
```sql
-- إضافة حقول جديدة لجدول العملاء
ALTER TABLE customers ADD COLUMN custom_field VARCHAR(255);
```

### تخصيص التقارير
```php
// إنشاء تقرير مخصص في
modules/reports/custom_report.php
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### خطأ في الاتصال بقاعدة البيانات
```
الحل: التأكد من صحة بيانات الاتصال في config/database.php
```

#### صفحة بيضاء أو خطأ 500
```
الحل: تفعيل عرض الأخطاء في PHP وفحص ملف error.log
```

#### مشاكل في الصلاحيات
```
الحل: التأكد من صلاحيات المجلدات والملفات
chmod 755 uploads/ backups/
```

#### بطء في التحميل
```
الحل: تحسين قاعدة البيانات وإضافة فهارس
```

## 📞 الدعم والمساعدة

### التواصل
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +962-6-1234567
- **الموقع**: www.accounting-system.com

### الوثائق
- **دليل المستخدم**: [docs/user-guide.pdf](docs/user-guide.pdf)
- **دليل المطور**: [docs/developer-guide.pdf](docs/developer-guide.pdf)
- **API Documentation**: [docs/api.md](docs/api.md)

## 🔄 التحديثات

### الإصدار الحالي: 1.0.0
- إطلاق النسخة الأولى
- جميع الميزات الأساسية
- دعم اللغة العربية

### التحديثات القادمة
- **الإصدار 1.1.0**: تطبيق جوال
- **الإصدار 1.2.0**: دعم الباركود
- **الإصدار 1.3.0**: تكامل مع البنوك

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف [LICENSE](LICENSE) للمزيد من التفاصيل.

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى قراءة [CONTRIBUTING.md](CONTRIBUTING.md) لمعرفة كيفية المساهمة في المشروع.

## 🙏 شكر وتقدير

- **Bootstrap**: لإطار العمل الأمامي
- **Font Awesome**: للأيقونات
- **Chart.js**: للرسوم البيانية
- **DataTables**: لجداول البيانات التفاعلية
- **jQuery**: لمكتبة JavaScript

---

**تم تطوير هذا النظام بعناية فائقة ليلبي احتياجات الشركات والمؤسسات في إدارة حساباتها المالية بكفاءة وأمان.**

**للمزيد من المعلومات أو الدعم التقني، لا تترددوا في التواصل معنا.**
