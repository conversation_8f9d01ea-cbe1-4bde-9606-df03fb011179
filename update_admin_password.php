<?php
require_once 'config/database.php';

echo "<h2>تحديث كلمة مرور المدير</h2>";

try {
    // البيانات الجديدة
    $newUsername = 'amam';
    $newPassword = '123456';
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    
    echo "<p>🔄 جاري تحديث بيانات المدير...</p>";
    
    // التحقق من وجود المستخدم admin
    $adminUser = fetchOne("SELECT * FROM users WHERE username = 'admin'");
    
    if ($adminUser) {
        // تحديث البيانات الموجودة
        $sql = "UPDATE users SET username = ?, password = ? WHERE username = 'admin'";
        executeQuery($sql, [$newUsername, $hashedPassword]);
        
        echo "<p style='color: green;'>✅ تم تحديث بيانات المدير بنجاح!</p>";
    } else {
        // إنشاء مستخدم جديد
        $sql = "INSERT INTO users (username, password, full_name, user_type, is_active) 
                VALUES (?, ?, 'مدير النظام', 'admin', TRUE)";
        executeQuery($sql, [$newUsername, $hashedPassword]);
        
        echo "<p style='color: green;'>✅ تم إنشاء المدير الجديد بنجاح!</p>";
    }
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>بيانات تسجيل الدخول الجديدة:</h3>";
    echo "<p><strong>اسم المستخدم:</strong> $newUsername</p>";
    echo "<p><strong>كلمة المرور:</strong> $newPassword</p>";
    echo "</div>";
    
    // عرض جميع المستخدمين
    echo "<h3>جميع المستخدمين في النظام:</h3>";
    $users = fetchAll("SELECT id, username, full_name, user_type, is_active, created_at FROM users");
    
    if (!empty($users)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f2f2f2;'>";
        echo "<th>ID</th><th>اسم المستخدم</th><th>الاسم الكامل</th><th>نوع المستخدم</th><th>نشط</th><th>تاريخ الإنشاء</th>";
        echo "</tr>";
        
        foreach ($users as $user) {
            $activeStatus = $user['is_active'] ? 'نعم' : 'لا';
            $activeColor = $user['is_active'] ? 'green' : 'red';
            
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td><strong>{$user['username']}</strong></td>";
            echo "<td>{$user['full_name']}</td>";
            echo "<td>{$user['user_type']}</td>";
            echo "<td style='color: $activeColor;'>$activeStatus</td>";
            echo "<td>{$user['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>لا توجد مستخدمين في النظام</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث كلمة مرور المدير</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
            max-width: 800px; 
            margin: 0 auto; 
        }
        table { 
            width: 100%; 
            margin: 10px 0; 
        }
        th, td { 
            padding: 8px; 
            text-align: right; 
            border: 1px solid #ddd;
        }
        th { 
            background: #f8f9fa; 
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            display: inline-block;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div style="text-align: center; margin-top: 20px;">
            <a href="login.php" class="btn">تسجيل الدخول</a>
            <a href="add_branch.php" class="btn">إضافة مركز</a>
            <a href="index.php" class="btn">الصفحة الرئيسية</a>
        </div>
        
        <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0; border: 1px solid #ffeaa7;">
            <h4>ملاحظة مهمة:</h4>
            <p>تم تغيير بيانات تسجيل الدخول. استخدم البيانات الجديدة للدخول إلى النظام.</p>
            <p>يمكنك حذف هذا الملف بعد التأكد من عمل تسجيل الدخول.</p>
        </div>
    </div>
</body>
</html>
