<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

checkSession();

$user = getUserById($_SESSION['user_id']);

// التحقق من الصلاحيات
if (!isAdmin($_SESSION['user_id'])) {
    header('Location: index.php?error=access_denied');
    exit();
}

$error = '';
$success = '';

// جلب الشركات
try {
    $companies = fetchAll("SELECT * FROM companies WHERE is_active = 1 ORDER BY name");
} catch (Exception $e) {
    $companies = [];
}

// دالة توليد كود الفرع التلقائي
function generateBranchCode($name) {
    // استخراج الأحرف الأولى من اسم الفرع
    $words = explode(' ', trim($name));
    $prefix = '';

    foreach ($words as $word) {
        if (!empty($word)) {
            // تحويل الأحرف العربية إلى إنجليزية
            $arabicToEnglish = [
                'ا' => 'A', 'أ' => 'A', 'إ' => 'A', 'آ' => 'A',
                'ب' => 'B', 'ت' => 'T', 'ث' => 'TH', 'ج' => 'J',
                'ح' => 'H', 'خ' => 'KH', 'د' => 'D', 'ذ' => 'DH',
                'ر' => 'R', 'ز' => 'Z', 'س' => 'S', 'ش' => 'SH',
                'ص' => 'S', 'ض' => 'D', 'ط' => 'T', 'ظ' => 'Z',
                'ع' => 'A', 'غ' => 'GH', 'ف' => 'F', 'ق' => 'Q',
                'ك' => 'K', 'ل' => 'L', 'م' => 'M', 'ن' => 'N',
                'ه' => 'H', 'و' => 'W', 'ي' => 'Y', 'ى' => 'Y',
                'ة' => 'H'
            ];

            $firstChar = mb_substr($word, 0, 1);
            if (isset($arabicToEnglish[$firstChar])) {
                $prefix .= $arabicToEnglish[$firstChar];
            } else {
                $prefix .= strtoupper($firstChar);
            }
        }
    }

    // التأكد من أن الكود لا يقل عن 3 أحرف
    if (strlen($prefix) < 3) {
        $prefix = strtoupper(substr(md5($name), 0, 3));
    }

    // قطع الكود إلى 3-4 أحرف كحد أقصى
    $prefix = substr($prefix, 0, 4);

    // البحث عن رقم متاح
    $counter = 1;
    do {
        $code = $prefix . sprintf('%03d', $counter);
        $existing = fetchOne("SELECT id FROM branches WHERE code = ?", [$code]);
        $counter++;
    } while ($existing && $counter <= 999);

    return $code;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // اسم الإدارة ثابت
        $departmentName = 'إدارة المراكز إصلاح والتأهيل';
        $name = trim($_POST['name']);
        $address = trim($_POST['address']);
        $phone = trim($_POST['phone']);
        $managerName = trim($_POST['manager_name']);

        // التحقق من صحة البيانات
        if (empty($name)) {
            throw new Exception('يرجى إدخال اسم المركز');
        }

        // توليد كود المركز تلقائياً
        $code = generateBranchCode($name);

        // إدراج المركز الجديد
        $sql = "INSERT INTO branches (name, code, address, phone, manager_name, created_at)
                VALUES (?, ?, ?, ?, ?, NOW())";

        executeQuery($sql, [$name, $code, $address, $phone, $managerName]);

        $branchId = getLastInsertId();

        // تسجيل النشاط
        logActivity($_SESSION['user_id'], 'إضافة مركز', "تم إضافة مركز جديد: $name ($code)");

        $success = 'تم إضافة المركز بنجاح';

        // إعادة توجيه إلى صفحة القائمة
        header("Location: branches.php?success=" . urlencode($success));
        exit();

    } catch (Exception $e) {
        // تسجيل الخطأ في اللوج
        error_log("Error adding branch: " . $e->getMessage());

        // عرض رسالة خطأ مفصلة
        $errorMsg = $e->getMessage();

        if (strpos($errorMsg, 'Duplicate entry') !== false) {
            $error = 'هذا الكود موجود مسبقاً. يرجى تغيير اسم المركز.';
        } elseif (strpos($errorMsg, 'Table') !== false && strpos($errorMsg, "doesn't exist") !== false) {
            $error = 'جدول قاعدة البيانات غير موجود. <a href="setup_database.php" class="btn btn-primary btn-sm">إعداد قاعدة البيانات</a>';
        } elseif (strpos($errorMsg, 'Access denied') !== false || strpos($errorMsg, 'Connection refused') !== false) {
            $error = 'خطأ في الاتصال بقاعدة البيانات. <a href="test_connection.php" class="btn btn-info btn-sm">اختبار الاتصال</a>';
        } else {
            $error = 'حدث خطأ أثناء إضافة المركز: ' . htmlspecialchars($errorMsg) . '<br><a href="test_connection.php" class="btn btn-warning btn-sm mt-2">فحص الاتصال</a>';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة مركز جديد - نظام المحاسبة المالية</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">

    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body { font-family: 'Cairo', sans-serif; }

        /* تخطيط القائمة على اليمين */
        @media (min-width: 768px) {
            .main-content-with-right-sidebar {
                margin-right: 300px;
                padding-right: 20px;
                padding-left: 20px;
            }
        }

        @media (max-width: 767.98px) {
            .main-content-with-right-sidebar {
                margin-right: 0;
                padding-right: 15px;
                padding-left: 15px;
            }
        }

        .form-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .form-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 20px;
            margin: -20px -20px 20px -20px;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .required {
            color: #dc3545;
        }

        .help-text {
            font-size: 0.875rem;
            color: #6c757d;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        #codePreview {
            transition: all 0.3s ease;
        }

        .auto-code-info {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Main Content Area -->
            <main class="col-12 px-md-4 main-content-with-right-sidebar">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-plus me-2"></i>
                        إضافة مركز جديد
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="branches.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right"></i> العودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>

                <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- نموذج إضافة الفرع -->
                <div class="row">
                    <div class="col-lg-8">
                        <div class="form-container p-4">
                            <div class="form-header">
                                <h4 class="mb-0">
                                    <i class="fas fa-building me-2"></i>
                                    بيانات المركز الجديد
                                </h4>
                            </div>

                            <form method="POST" id="branchForm" novalidate>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="department_name" class="form-label">اسم الإدارة</label>
                                            <input type="text" class="form-control readonly-field" id="department_name"
                                                   value="إدارة المراكز إصلاح والتأهيل" readonly>
                                            <div class="help-text">اسم الإدارة ثابت ولا يتغير</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">كود المركز</label>
                                            <div class="alert alert-info d-flex align-items-center">
                                                <i class="fas fa-magic me-2"></i>
                                                <div class="flex-grow-1">
                                                    <strong>سيتم توليد الكود تلقائياً</strong><br>
                                                    <small>بناءً على اسم المركز المدخل</small>
                                                </div>
                                                <div id="codePreview" class="badge bg-primary fs-6" style="display: none;">
                                                    <i class="fas fa-eye me-1"></i>
                                                    <span id="previewText"></span>
                                                </div>
                                            </div>
                                            <div class="help-text">مثال: "مركز عمان" سيصبح "MA001"</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="name" class="form-label">اسم المركز <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name"
                                           value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>"
                                           placeholder="مثال: مركز عمان المركزي" required maxlength="100">
                                    <div class="invalid-feedback">
                                        يرجى إدخال اسم المركز
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="address" class="form-label">العنوان</label>
                                    <textarea class="form-control" id="address" name="address" rows="3"
                                              placeholder="العنوان التفصيلي للمركز"><?php echo htmlspecialchars($_POST['address'] ?? ''); ?></textarea>
                                    <div class="help-text">العنوان الكامل للمركز مع تفاصيل الموقع</div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="phone" class="form-label">رقم الهاتف</label>
                                            <input type="tel" class="form-control" id="phone" name="phone"
                                                   value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>"
                                                   placeholder="+962-6-1234567" maxlength="20">
                                            <div class="help-text">رقم الهاتف الرئيسي للمركز</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="manager_name" class="form-label">اسم رئيس الفرع المالي</label>
                                            <input type="text" class="form-control" id="manager_name" name="manager_name"
                                                   value="<?php echo htmlspecialchars($_POST['manager_name'] ?? ''); ?>"
                                                   placeholder="اسم رئيس الفرع المالي" maxlength="100">
                                            <div class="help-text">اسم الشخص المسؤول عن الشؤون المالية في المركز</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                                    <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                        <i class="fas fa-undo me-2"></i>
                                        إعادة تعيين
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ المركز
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-lightbulb me-2"></i>
                                    نصائح مهمة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-magic me-2"></i>ميزة الكود التلقائي</h6>
                                    <ul class="mb-0">
                                        <li>يتم توليد الكود تلقائياً من اسم المركز</li>
                                        <li>الكود فريد ولا يمكن تعديله لاحقاً</li>
                                        <li>يمكن إضافة نقاط البيع بعد إنشاء المركز</li>
                                        <li>جميع الحقول المطلوبة مميزة بـ <span class="required">*</span></li>
                                    </ul>
                                </div>

                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيه</h6>
                                    <p class="mb-0">تأكد من صحة البيانات قبل الحفظ، حيث أن بعض البيانات مثل الكود لا يمكن تعديلها لاحقاً.</p>
                                </div>
                            </div>
                        </div>

                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-list me-2"></i>
                                    الخطوات التالية
                                </h5>
                            </div>
                            <div class="card-body">
                                <ol>
                                    <li>إنشاء المركز</li>
                                    <li>إضافة نقاط البيع للمركز</li>
                                    <li>إنشاء مستخدمين للمركز</li>
                                    <li>تحديد الصلاحيات</li>
                                    <li>بدء العمل</li>
                                </ol>
                            </div>
                        </div>

                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-code me-2"></i>
                                    أمثلة على توليد الأكواد
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="auto-code-info mb-3">
                                    <h6><i class="fas fa-lightbulb me-2"></i>كيف يعمل التوليد التلقائي؟</h6>
                                    <small>يتم أخذ الحرف الأول من كل كلمة في اسم المركز وتحويله إلى أحرف إنجليزية مع إضافة رقم تسلسلي</small>
                                </div>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <strong>"مركز عمان"</strong> → <code class="bg-primary text-white px-2 py-1 rounded">MA001</code>
                                        <br><small class="text-muted">م = M, ع = A + 001</small>
                                    </li>
                                    <li class="mb-2">
                                        <strong>"مركز إربد المركزي"</strong> → <code class="bg-success text-white px-2 py-1 rounded">MAM001</code>
                                        <br><small class="text-muted">م = M, إ = A, م = M + 001</small>
                                    </li>
                                    <li class="mb-2">
                                        <strong>"مركز الزرقاء"</strong> → <code class="bg-info text-white px-2 py-1 rounded">MZ001</code>
                                        <br><small class="text-muted">م = M, ز = Z + 001</small>
                                    </li>
                                    <li class="mb-2">
                                        <strong>"مركز العقبة"</strong> → <code class="bg-warning text-dark px-2 py-1 rounded">MA001</code>
                                        <br><small class="text-muted">م = M, ع = A + 001</small>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </main>

            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // دالة توليد معاينة الكود
        function generateCodePreview(name) {
            if (!name.trim()) {
                return '';
            }

            const words = name.trim().split(' ');
            let prefix = '';

            // خريطة تحويل الأحرف العربية
            const arabicToEnglish = {
                'ا': 'A', 'أ': 'A', 'إ': 'A', 'آ': 'A',
                'ب': 'B', 'ت': 'T', 'ث': 'TH', 'ج': 'J',
                'ح': 'H', 'خ': 'KH', 'د': 'D', 'ذ': 'DH',
                'ر': 'R', 'ز': 'Z', 'س': 'S', 'ش': 'SH',
                'ص': 'S', 'ض': 'D', 'ط': 'T', 'ظ': 'Z',
                'ع': 'A', 'غ': 'GH', 'ف': 'F', 'ق': 'Q',
                'ك': 'K', 'ل': 'L', 'م': 'M', 'ن': 'N',
                'ه': 'H', 'و': 'W', 'ي': 'Y', 'ى': 'Y',
                'ة': 'H'
            };

            words.forEach(word => {
                if (word) {
                    const firstChar = word.charAt(0);
                    if (arabicToEnglish[firstChar]) {
                        prefix += arabicToEnglish[firstChar];
                    } else {
                        prefix += firstChar.toUpperCase();
                    }
                }
            });

            // التأكد من أن الكود لا يقل عن 3 أحرف
            if (prefix.length < 3) {
                prefix = name.substring(0, 3).toUpperCase();
            }

            // قطع الكود إلى 4 أحرف كحد أقصى
            prefix = prefix.substring(0, 4);

            return prefix + '001';
        }

        // إعادة تعيين النموذج
        function resetForm() {
            if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟ سيتم فقدان جميع البيانات المدخلة.')) {
                document.getElementById('branchForm').reset();
                // إزالة classes التحقق
                document.querySelectorAll('.is-valid, .is-invalid').forEach(el => {
                    el.classList.remove('is-valid', 'is-invalid');
                });
                // إخفاء معاينة الكود
                document.getElementById('codePreview').style.display = 'none';
            }
        }

        // التحقق من صحة النموذج
        (function() {
            'use strict';

            const form = document.getElementById('branchForm');

            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }

                // التحقق من تنسيق الكود
                const codeInput = document.getElementById('code');
                const codeValue = codeInput.value.trim();

                if (codeValue && !/^[A-Z0-9]{3,10}$/.test(codeValue)) {
                    event.preventDefault();
                    codeInput.setCustomValidity('كود الفرع يجب أن يحتوي على أحرف إنجليزية كبيرة وأرقام فقط (3-10 أحرف)');
                } else {
                    codeInput.setCustomValidity('');
                }

                form.classList.add('was-validated');
            }, false);
        })();

        // معاينة الكود عند إدخال اسم المركز
        document.getElementById('name').addEventListener('input', function() {
            const name = this.value.trim();
            const codePreview = document.getElementById('codePreview');
            const previewText = document.getElementById('previewText');

            if (name) {
                const generatedCode = generateCodePreview(name);
                previewText.textContent = generatedCode;
                codePreview.style.display = 'block';

                // تأثير بصري
                codePreview.style.animation = 'pulse 0.5s ease-in-out';
                setTimeout(() => {
                    codePreview.style.animation = '';
                }, 500);
            } else {
                codePreview.style.display = 'none';
            }
        });

        // تنسيق رقم الهاتف
        document.getElementById('phone').addEventListener('input', function() {
            let value = this.value.replace(/[^\d+\-\s]/g, '');
            this.value = value;
        });

        // إضافة تأثيرات بصرية
        document.querySelectorAll('.form-control, .form-select').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
                this.parentElement.style.transition = 'transform 0.2s ease';
            });

            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>
