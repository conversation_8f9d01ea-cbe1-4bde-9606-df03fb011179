<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

checkSession();

$user = getUserById($_SESSION['user_id']);

// التحقق من الصلاحيات
if (!isAdmin($_SESSION['user_id'])) {
    header('Location: index.php?error=access_denied');
    exit();
}

$error = '';
$success = '';

// جلب الشركات
try {
    $companies = fetchAll("SELECT * FROM companies WHERE is_active = 1 ORDER BY name");
} catch (Exception $e) {
    $companies = [];
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $companyId = !empty($_POST['company_id']) ? $_POST['company_id'] : null;
        $name = trim($_POST['name']);
        $code = trim($_POST['code']);
        $address = trim($_POST['address']);
        $phone = trim($_POST['phone']);
        $managerName = trim($_POST['manager_name']);
        
        // التحقق من صحة البيانات
        if (empty($name) || empty($code)) {
            throw new Exception('يرجى ملء جميع الحقول المطلوبة');
        }
        
        // التحقق من عدم تكرار الكود
        $existingBranch = fetchOne("SELECT id FROM branches WHERE code = ? AND is_active = 1", [$code]);
        if ($existingBranch) {
            throw new Exception('كود الفرع موجود مسبقاً');
        }
        
        // إدراج الفرع الجديد
        $sql = "INSERT INTO branches (company_id, name, code, address, phone, manager_name, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())";
        
        executeQuery($sql, [$companyId, $name, $code, $address, $phone, $managerName]);
        
        $branchId = getLastInsertId();
        
        // تسجيل النشاط
        logActivity($_SESSION['user_id'], 'إضافة فرع', "تم إضافة فرع جديد: $name ($code)");
        
        $success = 'تم إضافة الفرع بنجاح';
        
        // إعادة توجيه إلى صفحة القائمة
        header("Location: branches.php?success=" . urlencode($success));
        exit();
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة فرع جديد - نظام المحاسبة المالية</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body { font-family: 'Cairo', sans-serif; }
        
        /* تخطيط القائمة على اليمين */
        @media (min-width: 768px) {
            .main-content-with-right-sidebar {
                margin-right: 300px;
                padding-right: 20px;
                padding-left: 20px;
            }
        }
        
        @media (max-width: 767.98px) {
            .main-content-with-right-sidebar {
                margin-right: 0;
                padding-right: 15px;
                padding-left: 15px;
            }
        }
        
        .form-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .form-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 20px;
            margin: -20px -20px 20px -20px;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .required {
            color: #dc3545;
        }
        
        .help-text {
            font-size: 0.875rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>
    
    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Main Content Area -->
            <main class="col-12 px-md-4 main-content-with-right-sidebar">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-plus me-2"></i>
                        إضافة فرع جديد
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="branches.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right"></i> العودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>

                <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- نموذج إضافة الفرع -->
                <div class="row">
                    <div class="col-lg-8">
                        <div class="form-container p-4">
                            <div class="form-header">
                                <h4 class="mb-0">
                                    <i class="fas fa-store me-2"></i>
                                    بيانات الفرع الجديد
                                </h4>
                            </div>
                            
                            <form method="POST" id="branchForm" novalidate>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="company_id" class="form-label">الشركة</label>
                                            <select class="form-select" id="company_id" name="company_id">
                                                <option value="">اختر الشركة (اختياري)</option>
                                                <?php foreach ($companies as $company): ?>
                                                <option value="<?php echo $company['id']; ?>" 
                                                        <?php echo (isset($_POST['company_id']) && $_POST['company_id'] == $company['id']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($company['name']); ?>
                                                </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <div class="help-text">يمكن ترك هذا الحقل فارغاً إذا لم تكن تستخدم نظام الشركات</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="code" class="form-label">كود الفرع <span class="required">*</span></label>
                                            <input type="text" class="form-control" id="code" name="code" 
                                                   value="<?php echo htmlspecialchars($_POST['code'] ?? ''); ?>"
                                                   placeholder="مثال: ZRQ001" required maxlength="10">
                                            <div class="help-text">كود فريد للفرع (أحرف إنجليزية وأرقام فقط)</div>
                                            <div class="invalid-feedback">
                                                يرجى إدخال كود صحيح للفرع
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="name" class="form-label">اسم الفرع <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>"
                                           placeholder="مثال: فرع الزرقاء" required maxlength="100">
                                    <div class="invalid-feedback">
                                        يرجى إدخال اسم الفرع
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="address" class="form-label">العنوان</label>
                                    <textarea class="form-control" id="address" name="address" rows="3" 
                                              placeholder="العنوان التفصيلي للفرع"><?php echo htmlspecialchars($_POST['address'] ?? ''); ?></textarea>
                                    <div class="help-text">العنوان الكامل للفرع مع تفاصيل الموقع</div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="phone" class="form-label">رقم الهاتف</label>
                                            <input type="tel" class="form-control" id="phone" name="phone" 
                                                   value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>"
                                                   placeholder="+962-6-1234567" maxlength="20">
                                            <div class="help-text">رقم الهاتف الرئيسي للفرع</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="manager_name" class="form-label">اسم المدير</label>
                                            <input type="text" class="form-control" id="manager_name" name="manager_name" 
                                                   value="<?php echo htmlspecialchars($_POST['manager_name'] ?? ''); ?>"
                                                   placeholder="اسم مدير الفرع" maxlength="100">
                                            <div class="help-text">اسم الشخص المسؤول عن إدارة الفرع</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                                    <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                        <i class="fas fa-undo me-2"></i>
                                        إعادة تعيين
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ الفرع
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-lightbulb me-2"></i>
                                    نصائح مهمة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة</h6>
                                    <ul class="mb-0">
                                        <li>كود الفرع يجب أن يكون فريداً</li>
                                        <li>يُفضل استخدام أكواد قصيرة ومفهومة</li>
                                        <li>يمكن إضافة نقاط البيع بعد إنشاء الفرع</li>
                                        <li>جميع الحقول المطلوبة مميزة بـ <span class="required">*</span></li>
                                    </ul>
                                </div>
                                
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيه</h6>
                                    <p class="mb-0">تأكد من صحة البيانات قبل الحفظ، حيث أن بعض البيانات مثل الكود لا يمكن تعديلها لاحقاً.</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-list me-2"></i>
                                    الخطوات التالية
                                </h5>
                            </div>
                            <div class="card-body">
                                <ol>
                                    <li>إنشاء الفرع</li>
                                    <li>إضافة نقاط البيع للفرع</li>
                                    <li>إنشاء مستخدمين للفرع</li>
                                    <li>تحديد الصلاحيات</li>
                                    <li>بدء العمل</li>
                                </ol>
                            </div>
                        </div>
                        
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-examples me-2"></i>
                                    أمثلة على الأكواد
                                </h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li><code>ZRQ001</code> - فرع الزرقاء الأول</li>
                                    <li><code>AMN001</code> - فرع عمان الأول</li>
                                    <li><code>IRB001</code> - فرع إربد الأول</li>
                                    <li><code>AQB001</code> - فرع العقبة الأول</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
            
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // إعادة تعيين النموذج
        function resetForm() {
            if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟ سيتم فقدان جميع البيانات المدخلة.')) {
                document.getElementById('branchForm').reset();
                // إزالة classes التحقق
                document.querySelectorAll('.is-valid, .is-invalid').forEach(el => {
                    el.classList.remove('is-valid', 'is-invalid');
                });
            }
        }
        
        // التحقق من صحة النموذج
        (function() {
            'use strict';
            
            const form = document.getElementById('branchForm');
            
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                
                // التحقق من تنسيق الكود
                const codeInput = document.getElementById('code');
                const codeValue = codeInput.value.trim();
                
                if (codeValue && !/^[A-Z0-9]{3,10}$/.test(codeValue)) {
                    event.preventDefault();
                    codeInput.setCustomValidity('كود الفرع يجب أن يحتوي على أحرف إنجليزية كبيرة وأرقام فقط (3-10 أحرف)');
                } else {
                    codeInput.setCustomValidity('');
                }
                
                form.classList.add('was-validated');
            }, false);
        })();
        
        // تحويل كود الفرع إلى أحرف كبيرة تلقائياً
        document.getElementById('code').addEventListener('input', function() {
            this.value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
        });
        
        // تنسيق رقم الهاتف
        document.getElementById('phone').addEventListener('input', function() {
            let value = this.value.replace(/[^\d+\-\s]/g, '');
            this.value = value;
        });
        
        // إضافة تأثيرات بصرية
        document.querySelectorAll('.form-control, .form-select').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
                this.parentElement.style.transition = 'transform 0.2s ease';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>
