<?php
require_once 'config/database.php';

echo "<h2>تحديث جدول المستخدمين</h2>";

try {
    // إضافة عمود القسم إذا لم يكن موجود
    try {
        executeQuery("ALTER TABLE users ADD COLUMN department VARCHAR(255) NULL AFTER branch_id");
        echo "<p style='color: green;'>✅ تم إضافة عمود القسم</p>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate column') !== false) {
            echo "<p style='color: orange;'>⚠️ عمود القسم موجود مسبقاً</p>";
        } else {
            throw $e;
        }
    }
    
    // تحديث جدول activity_logs لإضافة الأعمدة المفقودة
    try {
        executeQuery("ALTER TABLE activity_logs ADD COLUMN table_name VARCHAR(100) NULL AFTER description");
        echo "<p style='color: green;'>✅ تم إضافة عمود table_name</p>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate column') !== false) {
            echo "<p style='color: orange;'>⚠️ عمود table_name موجود مسبقاً</p>";
        }
    }
    
    try {
        executeQuery("ALTER TABLE activity_logs ADD COLUMN record_id INT NULL AFTER table_name");
        echo "<p style='color: green;'>✅ تم إضافة عمود record_id</p>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate column') !== false) {
            echo "<p style='color: orange;'>⚠️ عمود record_id موجود مسبقاً</p>";
        }
    }
    
    // عرض هيكل الجدول المحدث
    echo "<h3>هيكل جدول users المحدث:</h3>";
    $columns = fetchAll("DESCRIBE users");
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f2f2f2;'>";
    echo "<th>اسم العمود</th><th>النوع</th><th>NULL</th><th>المفتاح</th><th>القيمة الافتراضية</th>";
    echo "</tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td><strong>{$column['Field']}</strong></td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 20px 0; border: 1px solid #c3e6cb;'>";
    echo "<h3 style='color: #155724;'>🎉 تم تحديث قاعدة البيانات بنجاح!</h3>";
    echo "<p>الآن يمكنك استخدام نظام إدارة المستخدمين.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث جدول المستخدمين</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
            max-width: 900px; 
            margin: 0 auto; 
        }
        table { 
            width: 100%; 
            margin: 10px 0; 
        }
        th, td { 
            padding: 8px; 
            text-align: right; 
            border: 1px solid #ddd;
        }
        th { 
            background: #f8f9fa; 
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            display: inline-block;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <div style="text-align: center; margin-top: 20px;">
            <a href="manage_users.php" class="btn btn-success">فتح نظام إدارة المستخدمين</a>
            <a href="index.php" class="btn">الصفحة الرئيسية</a>
        </div>
    </div>
</body>
</html>
