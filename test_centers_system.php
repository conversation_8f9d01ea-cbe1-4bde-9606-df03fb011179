<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

checkSession();
$user = getUserById($_SESSION['user_id']);

// جلب إحصائيات المراكز
try {
    $stats = [
        'total_centers' => fetchOne("SELECT COUNT(*) as count FROM branches")['count'] ?? 0,
        'active_centers' => fetchOne("SELECT COUNT(*) as count FROM branches WHERE is_active = 1")['count'] ?? 0,
        'total_pos' => fetchOne("SELECT COUNT(*) as count FROM point_of_sales")['count'] ?? 0,
        'active_pos' => fetchOne("SELECT COUNT(*) as count FROM point_of_sales WHERE is_active = 1")['count'] ?? 0,
    ];
    
    // آخر المراكز المضافة
    $recentCenters = fetchAll("SELECT * FROM branches ORDER BY created_at DESC LIMIT 3");
    
} catch (Exception $e) {
    $stats = array_fill_keys(['total_centers', 'active_centers', 'total_pos', 'active_pos'], 0);
    $recentCenters = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المراكز - إدارة المراكز إصلاح والتأهيل</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body { 
            font-family: 'Cairo', sans-serif; 
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
        }
        
        /* تخطيط القائمة على اليمين */
        @media (min-width: 768px) {
            .main-content-with-right-sidebar {
                margin-right: 300px;
                padding-right: 20px;
                padding-left: 20px;
            }
        }
        
        @media (max-width: 767.98px) {
            .main-content-with-right-sidebar {
                margin-right: 0;
                padding-right: 15px;
                padding-left: 15px;
            }
        }
        
        .hero-section {
            background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.9));
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .department-badge {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.2em;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 3em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .feature-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .feature-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #3498db;
            transition: all 0.3s ease;
        }
        
        .feature-item:hover {
            background: #e9ecef;
            transform: translateX(-5px);
        }
        
        .center-item {
            background: linear-gradient(135deg, #ecf0f1, #bdc3c7);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #e74c3c;
        }
        
        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }
        
        .action-btn {
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: bold;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>
    
    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Main Content Area -->
            <main class="col-12 px-md-4 main-content-with-right-sidebar">
                <!-- Hero Section -->
                <div class="hero-section">
                    <div class="department-badge pulse">
                        <i class="fas fa-building me-3"></i>
                        إدارة المراكز إصلاح والتأهيل
                    </div>
                    
                    <h1 class="display-4 mb-4">
                        <i class="fas fa-university me-3 text-primary"></i>
                        نظام إدارة المراكز
                    </h1>
                    <p class="lead mb-4">
                        نظام متطور لإدارة مراكز الإصلاح والتأهيل مع توليد أكواد تلقائية وإدارة شاملة
                    </p>
                    
                    <div class="row justify-content-center">
                        <div class="col-md-8">
                            <div class="alert alert-info">
                                <h5><i class="fas fa-info-circle me-2"></i>معلومات النظام</h5>
                                <ul class="mb-0 text-start">
                                    <li><strong>الإدارة:</strong> إدارة المراكز إصلاح والتأهيل (ثابت)</li>
                                    <li><strong>نوع المؤسسات:</strong> مراكز إصلاح وتأهيل</li>
                                    <li><strong>المسؤول المالي:</strong> رئيس الفرع المالي</li>
                                    <li><strong>نظام الأكواد:</strong> توليد تلقائي من اسم المركز</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number text-primary"><?php echo $stats['total_centers']; ?></div>
                        <div class="text-muted">إجمالي المراكز</div>
                        <small class="text-muted">في النظام</small>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number text-success"><?php echo $stats['active_centers']; ?></div>
                        <div class="text-muted">المراكز النشطة</div>
                        <small class="text-muted">قيد التشغيل</small>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number text-info"><?php echo $stats['total_pos']; ?></div>
                        <div class="text-muted">نقاط البيع</div>
                        <small class="text-muted">في جميع المراكز</small>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number text-warning"><?php echo $stats['active_pos']; ?></div>
                        <div class="text-muted">نقاط البيع النشطة</div>
                        <small class="text-muted">قيد التشغيل</small>
                    </div>
                </div>

                <!-- المميزات الجديدة -->
                <div class="feature-section">
                    <h2 class="text-center mb-4">
                        <i class="fas fa-star text-warning me-2"></i>
                        المميزات المحدثة للمراكز
                    </h2>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="feature-item">
                                <h5><i class="fas fa-building text-danger me-2"></i>إدارة ثابتة</h5>
                                <p class="mb-0">اسم الإدارة ثابت: "إدارة المراكز إصلاح والتأهيل" ولا يتغير</p>
                            </div>
                            
                            <div class="feature-item">
                                <h5><i class="fas fa-code text-primary me-2"></i>كود المركز</h5>
                                <p class="mb-0">توليد تلقائي لكود المركز من اسم المركز مع حماية من التعديل</p>
                            </div>
                            
                            <div class="feature-item">
                                <h5><i class="fas fa-university text-success me-2"></i>اسم المركز</h5>
                                <p class="mb-0">إدخال اسم المركز بدلاً من اسم الفرع (مثال: مركز عمان المركزي)</p>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="feature-item">
                                <h5><i class="fas fa-user-tie text-info me-2"></i>رئيس الفرع المالي</h5>
                                <p class="mb-0">تحديد اسم رئيس الفرع المالي بدلاً من مدير الفرع</p>
                            </div>
                            
                            <div class="feature-item">
                                <h5><i class="fas fa-shield-alt text-warning me-2"></i>حماية متقدمة</h5>
                                <p class="mb-0">نظام حماية شامل مع تسجيل جميع الأنشطة والتغييرات</p>
                            </div>
                            
                            <div class="feature-item">
                                <h5><i class="fas fa-chart-line text-secondary me-2"></i>تقارير مخصصة</h5>
                                <p class="mb-0">تقارير وإحصائيات مخصصة لطبيعة عمل مراكز الإصلاح والتأهيل</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- آخر المراكز المضافة -->
                <?php if (!empty($recentCenters)): ?>
                <div class="feature-section">
                    <h2 class="text-center mb-4">
                        <i class="fas fa-clock text-info me-2"></i>
                        آخر المراكز المضافة
                    </h2>
                    
                    <?php foreach ($recentCenters as $center): ?>
                    <div class="center-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">
                                    <i class="fas fa-university me-2"></i>
                                    <?php echo htmlspecialchars($center['name']); ?>
                                </h6>
                                <small class="text-muted">
                                    الكود: <code><?php echo htmlspecialchars($center['code']); ?></code> |
                                    تاريخ الإضافة: <?php echo date('Y-m-d H:i', strtotime($center['created_at'])); ?>
                                </small>
                                <?php if ($center['manager_name']): ?>
                                <br><small class="text-muted">
                                    رئيس الفرع المالي: <?php echo htmlspecialchars($center['manager_name']); ?>
                                </small>
                                <?php endif; ?>
                            </div>
                            <div>
                                <?php if ($center['is_active']): ?>
                                <span class="badge bg-success">نشط</span>
                                <?php else: ?>
                                <span class="badge bg-secondary">غير نشط</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>

                <!-- معلومات المستخدم -->
                <div class="feature-section">
                    <h2 class="text-center mb-4">
                        <i class="fas fa-user text-primary me-2"></i>
                        معلومات المستخدم الحالي
                    </h2>
                    
                    <div class="row justify-content-center">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-user-circle fa-4x text-primary"></i>
                                    </div>
                                    <h5><?php echo htmlspecialchars($user['full_name']); ?></h5>
                                    <p class="text-muted"><?php echo htmlspecialchars($user['user_type']); ?></p>
                                    
                                    <div class="mb-3">
                                        <?php if (isAdmin($_SESSION['user_id'])): ?>
                                        <span class="badge bg-danger fs-6">مدير النظام</span>
                                        <p class="mt-2 mb-0">
                                            <small>لديك صلاحية كاملة لإدارة جميع المراكز</small>
                                        </p>
                                        <?php elseif (isMainAccountant($_SESSION['user_id'])): ?>
                                        <span class="badge bg-warning fs-6">محاسب رئيسي</span>
                                        <p class="mt-2 mb-0">
                                            <small>يمكنك عرض جميع البيانات</small>
                                        </p>
                                        <?php else: ?>
                                        <span class="badge bg-info fs-6">مستخدم عادي</span>
                                        <p class="mt-2 mb-0">
                                            <small>يمكنك عرض مراكزك فقط</small>
                                        </p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أمثلة على أكواد المراكز -->
                <div class="feature-section">
                    <h2 class="text-center mb-4">
                        <i class="fas fa-code text-success me-2"></i>
                        أمثلة على أكواد المراكز
                    </h2>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5>أمثلة عربية:</h5>
                            <ul class="list-group">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>مركز عمان المركزي</span>
                                    <code class="bg-primary text-white px-2 py-1 rounded">MAM001</code>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>مركز إربد الشمالي</span>
                                    <code class="bg-success text-white px-2 py-1 rounded">MAS001</code>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>مركز الزرقاء</span>
                                    <code class="bg-info text-white px-2 py-1 rounded">MZ001</code>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>مركز العقبة الجنوبي</span>
                                    <code class="bg-warning text-dark px-2 py-1 rounded">MAJ001</code>
                                </li>
                            </ul>
                        </div>
                        
                        <div class="col-md-6">
                            <h5>أمثلة مختلطة:</h5>
                            <ul class="list-group">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>مركز الكرك الوسطى</span>
                                    <code class="bg-danger text-white px-2 py-1 rounded">MKW001</code>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>مركز مادبا</span>
                                    <code class="bg-secondary text-white px-2 py-1 rounded">MM001</code>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>مركز السلط</span>
                                    <code class="bg-dark text-white px-2 py-1 rounded">MS001</code>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>مركز جرش الأثري</span>
                                    <code class="bg-primary text-white px-2 py-1 rounded">MJA001</code>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- أزرار العمل -->
                <div class="action-buttons">
                    <a href="add_branch.php" class="action-btn btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة مركز جديد
                    </a>
                    <a href="branches.php" class="action-btn btn btn-success">
                        <i class="fas fa-list me-2"></i>
                        قائمة المراكز
                    </a>
                    <a href="test_auto_code.php" class="action-btn btn btn-warning">
                        <i class="fas fa-magic me-2"></i>
                        اختبار الكود التلقائي
                    </a>
                    <a href="index.php" class="action-btn btn btn-secondary">
                        <i class="fas fa-home me-2"></i>
                        الصفحة الرئيسية
                    </a>
                </div>
            </main>
            
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تأثيرات بصرية عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير على الإحصائيات
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach((card, index) => {
                setTimeout(() => {
                    const number = card.querySelector('.stat-number');
                    const finalValue = parseInt(number.textContent);
                    let currentValue = 0;
                    
                    const increment = finalValue / 50;
                    const timer = setInterval(() => {
                        currentValue += increment;
                        if (currentValue >= finalValue) {
                            currentValue = finalValue;
                            clearInterval(timer);
                        }
                        number.textContent = Math.floor(currentValue);
                    }, 30);
                }, index * 100);
            });
            
            // تأثير على الأزرار
            document.querySelectorAll('.action-btn').forEach(btn => {
                btn.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px) scale(1.05)';
                });
                
                btn.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
            
            // تأثير على عناصر المراكز
            document.querySelectorAll('.center-item').forEach((item, index) => {
                setTimeout(() => {
                    item.style.opacity = '0';
                    item.style.transform = 'translateX(50px)';
                    item.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        item.style.opacity = '1';
                        item.style.transform = 'translateX(0)';
                    }, 100);
                }, index * 200);
            });
        });
        
        // رسائل ترحيب
        setTimeout(() => {
            if (<?php echo $stats['total_centers']; ?> === 0) {
                if (confirm('مرحباً بك في نظام إدارة المراكز!\n\nلا توجد مراكز في النظام حالياً.\nهل تريد إضافة مركز جديد الآن؟')) {
                    window.location.href = 'add_branch.php';
                }
            }
        }, 2000);
    </script>
</body>
</html>
