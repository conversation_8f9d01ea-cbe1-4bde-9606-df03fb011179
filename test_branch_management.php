<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

checkSession();
$user = getUserById($_SESSION['user_id']);

// جلب إحصائيات الفروع
try {
    $totalBranches = fetchOne("SELECT COUNT(*) as count FROM branches")['count'] ?? 0;
    $activeBranches = fetchOne("SELECT COUNT(*) as count FROM branches WHERE is_active = 1")['count'] ?? 0;
    $totalPOS = fetchOne("SELECT COUNT(*) as count FROM point_of_sales")['count'] ?? 0;
    $activePOS = fetchOne("SELECT COUNT(*) as count FROM point_of_sales WHERE is_active = 1")['count'] ?? 0;
} catch (Exception $e) {
    $totalBranches = $activeBranches = $totalPOS = $activePOS = 0;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إدارة الفروع - نظام المحاسبة المالية</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">

    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }

        /* تخطيط القائمة على اليمين */
        @media (min-width: 768px) {
            .main-content-with-right-sidebar {
                margin-right: 300px;
                padding-right: 20px;
                padding-left: 20px;
            }
        }

        @media (max-width: 767.98px) {
            .main-content-with-right-sidebar {
                margin-right: 0;
                padding-right: 15px;
                padding-left: 15px;
            }
        }

        .feature-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            height: 100%;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 24px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }

        .test-section {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .success-badge {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            margin: 5px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Main Content Area -->
            <main class="col-12 px-md-4 main-content-with-right-sidebar">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-test-tube me-2"></i>
                        اختبار نظام إدارة الفروع
                    </h1>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <h3><?php echo $totalBranches; ?></h3>
                            <p>إجمالي الفروع</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <h3><?php echo $activeBranches; ?></h3>
                            <p>الفروع النشطة</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <h3><?php echo $totalPOS; ?></h3>
                            <p>إجمالي نقاط البيع</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <h3><?php echo $activePOS; ?></h3>
                            <p>نقاط البيع النشطة</p>
                        </div>
                    </div>
                </div>

                <!-- المميزات المتوفرة -->
                <div class="test-section">
                    <h3 class="mb-4">
                        <i class="fas fa-star me-2 text-warning"></i>
                        المميزات المتوفرة الآن
                    </h3>

                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="card feature-card text-center">
                                <div class="card-body">
                                    <div class="feature-icon bg-primary text-white">
                                        <i class="fas fa-plus"></i>
                                    </div>
                                    <h5>إضافة فرع جديد</h5>
                                    <p class="text-muted">إنشاء فروع جديدة مع جميع البيانات المطلوبة</p>
                                    <a href="add_branch.php" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>
                                        إضافة فرع
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="card feature-card text-center">
                                <div class="card-body">
                                    <div class="feature-icon bg-success text-white">
                                        <i class="fas fa-eye"></i>
                                    </div>
                                    <h5>عرض التفاصيل</h5>
                                    <p class="text-muted">عرض تفاصيل شاملة لكل فرع مع الإحصائيات</p>
                                    <a href="branches.php" class="btn btn-success">
                                        <i class="fas fa-list me-2"></i>
                                        قائمة الفروع
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="card feature-card text-center">
                                <div class="card-body">
                                    <div class="feature-icon bg-warning text-white">
                                        <i class="fas fa-edit"></i>
                                    </div>
                                    <h5>تعديل البيانات</h5>
                                    <p class="text-muted">تعديل بيانات الفروع مع حفظ التاريخ</p>
                                    <button class="btn btn-warning" onclick="testEdit()">
                                        <i class="fas fa-edit me-2"></i>
                                        اختبار التعديل
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="card feature-card text-center">
                                <div class="card-body">
                                    <div class="feature-icon bg-danger text-white">
                                        <i class="fas fa-trash"></i>
                                    </div>
                                    <h5>حذف آمن</h5>
                                    <p class="text-muted">حذف ناعم أو نهائي مع خيارات متقدمة</p>
                                    <button class="btn btn-danger" onclick="testDelete()">
                                        <i class="fas fa-trash me-2"></i>
                                        اختبار الحذف
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- اختبار الوظائف -->
                <div class="test-section">
                    <h3 class="mb-4">
                        <i class="fas fa-cogs me-2 text-info"></i>
                        اختبار الوظائف
                    </h3>

                    <div class="row">
                        <div class="col-md-6">
                            <h5>✅ الوظائف المكتملة:</h5>
                            <div class="success-badge">
                                <i class="fas fa-check me-2"></i>
                                إضافة فرع جديد
                            </div>
                            <div class="success-badge">
                                <i class="fas fa-check me-2"></i>
                                عرض قائمة الفروع
                            </div>
                            <div class="success-badge">
                                <i class="fas fa-check me-2"></i>
                                عرض تفاصيل الفرع
                            </div>
                            <div class="success-badge">
                                <i class="fas fa-check me-2"></i>
                                تعديل بيانات الفرع
                            </div>
                            <div class="success-badge">
                                <i class="fas fa-check me-2"></i>
                                حذف ناعم ونهائي
                            </div>
                            <div class="success-badge">
                                <i class="fas fa-check me-2"></i>
                                التحقق من الصلاحيات
                            </div>
                            <div class="success-badge">
                                <i class="fas fa-check me-2"></i>
                                تسجيل الأنشطة
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h5>🔗 الروابط السريعة:</h5>
                            <div class="list-group">
                                <a href="branches.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-list me-2"></i>
                                    قائمة الفروع الرئيسية
                                </a>
                                <a href="add_branch.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-plus me-2"></i>
                                    إضافة فرع جديد
                                </a>
                                <?php if ($totalBranches > 0): ?>
                                <a href="view_branch.php?id=1" class="list-group-item list-group-item-action">
                                    <i class="fas fa-eye me-2"></i>
                                    عرض تفاصيل فرع (مثال)
                                </a>
                                <a href="edit_branch.php?id=1" class="list-group-item list-group-item-action">
                                    <i class="fas fa-edit me-2"></i>
                                    تعديل فرع (مثال)
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- اختبار التفاعل -->
                <div class="test-section">
                    <h3 class="mb-4">
                        <i class="fas fa-mouse-pointer me-2 text-success"></i>
                        اختبار التفاعل
                    </h3>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">اختبار الإضافة</h6>
                                </div>
                                <div class="card-body">
                                    <p>اختبر إضافة فرع جديد مع جميع البيانات</p>
                                    <button class="btn btn-primary w-100" onclick="testAdd()">
                                        بدء الاختبار
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">اختبار العرض</h6>
                                </div>
                                <div class="card-body">
                                    <p>اختبر عرض قائمة الفروع والتفاصيل</p>
                                    <button class="btn btn-success w-100" onclick="testView()">
                                        بدء الاختبار
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0">اختبار التعديل</h6>
                                </div>
                                <div class="card-body">
                                    <p>اختبر تعديل بيانات فرع موجود</p>
                                    <button class="btn btn-warning w-100" onclick="testEdit()">
                                        بدء الاختبار
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات المستخدم -->
                <div class="test-section">
                    <h3 class="mb-4">
                        <i class="fas fa-user me-2 text-info"></i>
                        معلومات المستخدم الحالي
                    </h3>

                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <td><strong>الاسم:</strong></td>
                                    <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>نوع المستخدم:</strong></td>
                                    <td><?php echo htmlspecialchars($user['user_type']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>الصلاحيات:</strong></td>
                                    <td>
                                        <?php if (isAdmin($_SESSION['user_id'])): ?>
                                        <span class="badge bg-danger">مدير النظام</span>
                                        <?php elseif (isMainAccountant($_SESSION['user_id'])): ?>
                                        <span class="badge bg-warning">محاسب رئيسي</span>
                                        <?php else: ?>
                                        <span class="badge bg-info">مستخدم عادي</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>ملاحظات مهمة</h6>
                                <ul class="mb-0">
                                    <li>فقط المديرين يمكنهم إضافة وتعديل وحذف الفروع</li>
                                    <li>المحاسبين الرئيسيين يمكنهم عرض جميع البيانات</li>
                                    <li>المستخدمين العاديين يرون فروعهم فقط</li>
                                    <li>جميع العمليات مسجلة في سجل الأنشطة</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار التنقل -->
                <div class="text-center">
                    <a href="index.php" class="btn btn-secondary me-2">
                        <i class="fas fa-home me-2"></i>
                        الصفحة الرئيسية
                    </a>
                    <a href="branches.php" class="btn btn-primary me-2">
                        <i class="fas fa-store me-2"></i>
                        إدارة الفروع
                    </a>
                    <a href="pos.php" class="btn btn-success me-2">
                        <i class="fas fa-cash-register me-2"></i>
                        نقاط البيع
                    </a>
                    <a href="test_auto_code.php" class="btn btn-warning me-2">
                        <i class="fas fa-magic me-2"></i>
                        اختبار الكود التلقائي
                    </a>
                    <a href="test_sidebar_fixed.php" class="btn btn-info">
                        <i class="fas fa-test-tube me-2"></i>
                        اختبارات أخرى
                    </a>
                </div>
            </main>

            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function testAdd() {
            if (confirm('سيتم توجيهك إلى صفحة إضافة فرع جديد. هل تريد المتابعة؟')) {
                window.location.href = 'add_branch.php';
            }
        }

        function testView() {
            if (confirm('سيتم توجيهك إلى صفحة قائمة الفروع. هل تريد المتابعة؟')) {
                window.location.href = 'branches.php';
            }
        }

        function testEdit() {
            <?php if ($totalBranches > 0): ?>
            if (confirm('سيتم توجيهك إلى صفحة تعديل فرع (مثال). هل تريد المتابعة؟')) {
                window.location.href = 'edit_branch.php?id=1';
            }
            <?php else: ?>
            alert('لا توجد فروع في النظام لاختبار التعديل. يرجى إضافة فرع أولاً.');
            <?php endif; ?>
        }

        function testDelete() {
            <?php if ($totalBranches > 0): ?>
            if (confirm('سيتم توجيهك إلى صفحة خيارات الحذف (مثال). هل تريد المتابعة؟')) {
                window.location.href = 'delete_branch.php?id=1';
            }
            <?php else: ?>
            alert('لا توجد فروع في النظام لاختبار الحذف. يرجى إضافة فرع أولاً.');
            <?php endif; ?>
        }

        // إضافة تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير على البطاقات
            document.querySelectorAll('.feature-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.borderColor = '#007bff';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.borderColor = '#dee2e6';
                });
            });

            // تأثير على الأزرار
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 100);
                });
            });
        });
    </script>
</body>
</html>
