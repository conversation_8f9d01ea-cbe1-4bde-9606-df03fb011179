<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

checkSession();

$user = getUserById($_SESSION['user_id']);

// التحقق من الصلاحيات
if (!isAdmin($_SESSION['user_id'])) {
    header('Location: ../../index.php?error=access_denied');
    exit();
}

$error = '';
$success = '';

// جلب الفروع
$branches = getAllBranches();

// أنواع نقاط البيع
$posTypes = [
    'supermarket' => 'سوبر ماركت',
    'cafeteria' => 'كافتيريا',
    'nuts_shop' => 'محمص',
    'sweets' => 'حلويات',
    'bakery' => 'مخبز'
];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $branchId = $_POST['branch_id'] ?? '';
        $name = trim($_POST['name']);
        $type = $_POST['type'] ?? '';
        $description = trim($_POST['description']);
        
        // التحقق من صحة البيانات
        if (empty($branchId) || empty($name) || empty($type)) {
            throw new Exception('يرجى ملء جميع الحقول المطلوبة');
        }
        
        // التحقق من عدم تكرار الاسم في نفس الفرع
        $existingPos = fetchOne("SELECT id FROM point_of_sales WHERE branch_id = ? AND name = ?", [$branchId, $name]);
        if ($existingPos) {
            throw new Exception('اسم نقطة البيع موجود مسبقاً في هذا الفرع');
        }
        
        // إدراج نقطة البيع الجديدة
        $sql = "INSERT INTO point_of_sales (branch_id, name, type, description) 
                VALUES (?, ?, ?, ?)";
        
        executeQuery($sql, [$branchId, $name, $type, $description]);
        
        $posId = getLastInsertId();
        
        // تسجيل النشاط
        logActivity($_SESSION['user_id'], 'إضافة نقطة بيع', "تم إضافة نقطة بيع جديدة: $name");
        
        $success = 'تم إضافة نقطة البيع بنجاح';
        
        // إعادة توجيه إلى صفحة القائمة
        header("Location: list.php?success=1");
        exit();
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة نقطة بيع جديدة - نظام المحاسبة المالية</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include '../../includes/navbar.php'; ?>
    
    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>
            
            <!-- Main Content Area -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-plus me-2"></i>
                        إضافة نقطة بيع جديدة
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="list.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right"></i> العودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>

                <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
                <?php endif; ?>

                <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
                <?php endif; ?>

                <!-- نموذج إضافة نقطة البيع -->
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    بيانات نقطة البيع
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" id="posForm">
                                    <div class="mb-3">
                                        <label for="branch_id" class="form-label">الفرع *</label>
                                        <select class="form-select" id="branch_id" name="branch_id" required>
                                            <option value="">اختر الفرع</option>
                                            <?php foreach ($branches as $branch): ?>
                                            <option value="<?php echo $branch['id']; ?>">
                                                <?php echo htmlspecialchars($branch['name']); ?> (<?php echo htmlspecialchars($branch['code']); ?>)
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="name" class="form-label">اسم نقطة البيع *</label>
                                                <input type="text" class="form-control" id="name" name="name" 
                                                       placeholder="مثال: سوبر ماركت الزرقاء" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="type" class="form-label">نوع نقطة البيع *</label>
                                                <select class="form-select" id="type" name="type" required>
                                                    <option value="">اختر النوع</option>
                                                    <?php foreach ($posTypes as $typeKey => $typeName): ?>
                                                    <option value="<?php echo $typeKey; ?>">
                                                        <?php echo $typeName; ?>
                                                    </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="description" class="form-label">الوصف</label>
                                        <textarea class="form-control" id="description" name="description" rows="3" 
                                                  placeholder="وصف تفصيلي لنقطة البيع (اختياري)"></textarea>
                                    </div>
                                    
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                            <i class="fas fa-undo me-2"></i>
                                            إعادة تعيين
                                        </button>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>
                                            حفظ نقطة البيع
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    أنواع نقاط البيع
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php foreach ($posTypes as $typeKey => $typeName): ?>
                                <div class="d-flex align-items-center mb-2">
                                    <?php
                                    $typeIcons = [
                                        'supermarket' => 'fas fa-shopping-cart text-primary',
                                        'cafeteria' => 'fas fa-coffee text-warning',
                                        'nuts_shop' => 'fas fa-seedling text-success',
                                        'sweets' => 'fas fa-candy-cane text-danger',
                                        'bakery' => 'fas fa-bread-slice text-info'
                                    ];
                                    $icon = $typeIcons[$typeKey] ?? 'fas fa-store';
                                    ?>
                                    <i class="<?php echo $icon; ?> me-2"></i>
                                    <span><?php echo $typeName; ?></span>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-lightbulb me-2"></i>
                                    نصائح
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <ul class="mb-0">
                                        <li>اختر الفرع المناسب أولاً</li>
                                        <li>استخدم أسماء واضحة ومميزة</li>
                                        <li>حدد النوع المناسب لنشاط نقطة البيع</li>
                                        <li>يمكن إضافة مستخدمين لنقطة البيع لاحقاً</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-list me-2"></i>
                                    الخطوات التالية
                                </h5>
                            </div>
                            <div class="card-body">
                                <ol>
                                    <li>إنشاء نقطة البيع</li>
                                    <li>إضافة مستخدمين لنقطة البيع</li>
                                    <li>تحديد المنتجات المتاحة</li>
                                    <li>بدء المبيعات</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // إعادة تعيين النموذج
        function resetForm() {
            if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟ سيتم فقدان جميع البيانات المدخلة.')) {
                document.getElementById('posForm').reset();
            }
        }
        
        // التحقق من صحة النموذج
        document.getElementById('posForm').addEventListener('submit', function(e) {
            const branchId = document.getElementById('branch_id').value;
            const name = document.getElementById('name').value.trim();
            const type = document.getElementById('type').value;
            
            if (!branchId || !name || !type) {
                e.preventDefault();
                alert('يرجى ملء جميع الحقول المطلوبة');
                return false;
            }
        });
        
        // تحديث اسم نقطة البيع تلقائياً عند تغيير الفرع والنوع
        function updatePOSName() {
            const branchSelect = document.getElementById('branch_id');
            const typeSelect = document.getElementById('type');
            const nameInput = document.getElementById('name');
            
            if (branchSelect.value && typeSelect.value) {
                const branchName = branchSelect.options[branchSelect.selectedIndex].text.split(' (')[0];
                const typeName = typeSelect.options[typeSelect.selectedIndex].text;
                nameInput.value = typeName + ' ' + branchName;
            }
        }
        
        document.getElementById('branch_id').addEventListener('change', updatePOSName);
        document.getElementById('type').addEventListener('change', updatePOSName);
    </script>
</body>
</html>
