<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مرحباً بك - نظام المحاسبة المالية</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .welcome-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            margin: 20px;
        }

        .welcome-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .welcome-body {
            padding: 40px;
        }

        .feature-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .step-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="welcome-container">
        <div class="welcome-header">
            <h1><i class="fas fa-calculator me-3"></i>نظام المحاسبة المالية المتكامل</h1>
            <p class="mb-0 fs-5">مرحباً بك في النظام الأكثر تطوراً لإدارة الحسابات المالية</p>
        </div>

        <div class="welcome-body">
            <div class="row">
                <div class="col-lg-8">
                    <h3 class="mb-4">خطوات البدء السريع</h3>

                    <div class="feature-card">
                        <div class="d-flex align-items-center mb-3">
                            <span class="step-number">1</span>
                            <h5 class="mb-0">فحص النظام</h5>
                        </div>
                        <p class="mb-2">تحقق من أن جميع المتطلبات متوفرة ومثبتة بشكل صحيح</p>
                        <a href="check.php" class="btn btn-outline-primary btn-sm me-2">
                            <i class="fas fa-check-circle me-1"></i>
                            فحص النظام
                        </a>
                        <a href="debug.php" class="btn btn-outline-secondary btn-sm me-2">
                            <i class="fas fa-bug me-1"></i>
                            تشخيص متقدم
                        </a>
                        <a href="test_connection.php" class="btn btn-outline-info btn-sm me-2">
                            <i class="fas fa-database me-1"></i>
                            اختبار الاتصال
                        </a>
                        <a href="test_branches_link.php" class="btn btn-outline-warning btn-sm me-2">
                            <i class="fas fa-wrench me-1"></i>
                            اختبار الفروع
                        </a>
                        <a href="modules/branches/simple_test.php" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-play me-1"></i>
                            اختبار مباشر
                        </a>
                    </div>

                    <div class="feature-card">
                        <div class="d-flex align-items-center mb-3">
                            <span class="step-number">2</span>
                            <h5 class="mb-0">تثبيت النظام</h5>
                        </div>
                        <p class="mb-2">قم بتشغيل معالج التثبيت لإعداد قاعدة البيانات والإعدادات الأساسية</p>
                        <a href="install.php" class="btn btn-primary btn-sm">
                            <i class="fas fa-cog me-1"></i>
                            بدء التثبيت
                        </a>
                    </div>

                    <div class="feature-card">
                        <div class="d-flex align-items-center mb-3">
                            <span class="step-number">3</span>
                            <h5 class="mb-0">تسجيل الدخول</h5>
                        </div>
                        <p class="mb-2">استخدم البيانات الافتراضية لتسجيل الدخول والبدء في استخدام النظام</p>
                        <a href="login.php" class="btn btn-success btn-sm">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            تسجيل الدخول
                        </a>
                    </div>
                </div>

                <div class="col-lg-4">
                    <h3 class="mb-4">مميزات النظام</h3>

                    <div class="list-group">
                        <div class="list-group-item border-0 bg-transparent">
                            <i class="fas fa-store text-primary me-2"></i>
                            إدارة متعددة الفروع
                        </div>
                        <div class="list-group-item border-0 bg-transparent">
                            <i class="fas fa-file-invoice text-primary me-2"></i>
                            فواتير المبيعات والمشتريات
                        </div>
                        <div class="list-group-item border-0 bg-transparent">
                            <i class="fas fa-chart-pie text-primary me-2"></i>
                            تقارير مالية شاملة
                        </div>
                        <div class="list-group-item border-0 bg-transparent">
                            <i class="fas fa-users text-primary me-2"></i>
                            نظام صلاحيات متقدم
                        </div>
                        <div class="list-group-item border-0 bg-transparent">
                            <i class="fas fa-boxes text-primary me-2"></i>
                            إدارة المخزون
                        </div>
                        <div class="list-group-item border-0 bg-transparent">
                            <i class="fas fa-mobile-alt text-primary me-2"></i>
                            واجهة متجاوبة
                        </div>
                        <div class="list-group-item border-0 bg-transparent">
                            <i class="fas fa-shield-alt text-primary me-2"></i>
                            أمان عالي
                        </div>
                    </div>

                    <div class="mt-4 p-3 bg-light rounded">
                        <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة</h6>
                        <small class="text-muted">
                            تأكد من تشغيل XAMPP وتفعيل Apache و MySQL قبل البدء في التثبيت.
                            النظام يتطلب PHP 7.4 أو أحدث.
                        </small>
                    </div>
                </div>
            </div>

            <hr class="my-4">

            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-question-circle me-2"></i>تحتاج مساعدة؟</h5>
                    <p class="text-muted">راجع دليل المستخدم أو اتصل بفريق الدعم</p>
                    <a href="QUICK_START.md" class="btn btn-outline-info btn-sm me-2">دليل البدء السريع</a>
                    <a href="README.md" class="btn btn-outline-secondary btn-sm">الوثائق الكاملة</a>
                </div>
                <div class="col-md-6 text-end">
                    <h5><i class="fas fa-code me-2"></i>معلومات تقنية</h5>
                    <p class="text-muted">
                        الإصدار: 1.0.0<br>
                        PHP: <?php echo PHP_VERSION; ?><br>
                        التاريخ: <?php echo date('Y-m-d'); ?>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
