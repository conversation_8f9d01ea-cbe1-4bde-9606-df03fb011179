<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<h2>اختبار رابط الفروع ونقاط البيع</h2>";

// التحقق من الجلسة
if (!isset($_SESSION['user_id'])) {
    echo "<div style='color: red;'>✗ لم يتم تسجيل الدخول</div>";
    echo "<a href='login.php'>تسجيل الدخول</a>";
    exit();
}

echo "<div style='color: green;'>✓ تم تسجيل الدخول</div>";

// التحقق من المستخدم
$user = getUserById($_SESSION['user_id']);
if (!$user) {
    echo "<div style='color: red;'>✗ خطأ في بيانات المستخدم</div>";
    exit();
}

echo "<div style='color: green;'>✓ بيانات المستخدم صحيحة</div>";
echo "<p>المستخدم: " . htmlspecialchars($user['full_name']) . "</p>";
echo "<p>نوع المستخدم: " . htmlspecialchars($user['user_type']) . "</p>";

// التحقق من الصلاحيات
if (isAdmin($_SESSION['user_id'])) {
    echo "<div style='color: green;'>✓ المستخدم مدير - له صلاحية الوصول</div>";
} elseif (isMainAccountant($_SESSION['user_id'])) {
    echo "<div style='color: green;'>✓ المستخدم محاسب رئيسي - له صلاحية الوصول</div>";
} else {
    echo "<div style='color: orange;'>⚠ المستخدم ليس مدير أو محاسب رئيسي</div>";
}

// التحقق من وجود الملفات
echo "<h3>فحص الملفات:</h3>";
$files = [
    'modules/branches/list.php',
    'modules/branches/add.php',
    'modules/pos/list.php',
    'modules/pos/add.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "<div style='color: green;'>✓ $file موجود</div>";
    } else {
        echo "<div style='color: red;'>✗ $file غير موجود</div>";
    }
}

// التحقق من قاعدة البيانات
echo "<h3>فحص قاعدة البيانات:</h3>";
try {
    $branchCount = $pdo->query("SELECT COUNT(*) FROM branches")->fetchColumn();
    echo "<div style='color: green;'>✓ جدول الفروع موجود - عدد الفروع: $branchCount</div>";
    
    $posCount = $pdo->query("SELECT COUNT(*) FROM point_of_sales")->fetchColumn();
    echo "<div style='color: green;'>✓ جدول نقاط البيع موجود - عدد نقاط البيع: $posCount</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red;'>✗ خطأ في قاعدة البيانات: " . $e->getMessage() . "</div>";
}

// اختبار الروابط
echo "<h3>اختبار الروابط:</h3>";
echo "<p><a href='modules/branches/list.php' target='_blank'>فتح صفحة الفروع في نافذة جديدة</a></p>";
echo "<p><a href='modules/pos/list.php' target='_blank'>فتح صفحة نقاط البيع في نافذة جديدة</a></p>";

// إضافة بيانات تجريبية إذا لم تكن موجودة
if ($branchCount == 0) {
    echo "<h3>إضافة بيانات تجريبية:</h3>";
    try {
        // إضافة شركة
        $pdo->exec("INSERT IGNORE INTO companies (id, name, address, phone, email) VALUES 
                   (1, 'شركة التجارة المتقدمة', 'عمان - الأردن', '+962-6-1234567', '<EMAIL>')");
        
        // إضافة الفروع
        $pdo->exec("INSERT IGNORE INTO branches (id, company_id, name, code, address, phone, manager_name) VALUES 
                   (1, 1, 'فرع الزرقاء', 'ZRQ001', 'الزرقاء - شارع الملك عبدالله', '+962-5-1234567', 'أحمد محمد'),
                   (2, 1, 'فرع ماركا', 'MRK001', 'ماركا الشمالية - عمان', '+962-6-2345678', 'محمد أحمد'),
                   (3, 1, 'فرع قفقفا', 'QFQ001', 'قفقفا - الزرقاء', '+962-5-3456789', 'سالم علي')");
        
        // إضافة نقاط البيع
        $pdo->exec("INSERT IGNORE INTO point_of_sales (id, branch_id, name, type, description) VALUES 
                   (1, 1, 'سوبر ماركت الزرقاء', 'supermarket', 'سوبر ماركت شامل'),
                   (2, 1, 'كافتيريا الزرقاء', 'cafeteria', 'كافتيريا ومشروبات'),
                   (3, 1, 'محمص الزرقاء', 'nuts_shop', 'بزر ومكسرات'),
                   (4, 2, 'سوبر ماركت ماركا', 'supermarket', 'سوبر ماركت فرع ماركا'),
                   (5, 3, 'سوبر ماركت قفقفا', 'supermarket', 'سوبر ماركت فرع قفقفا')");
        
        echo "<div style='color: green;'>✓ تم إضافة البيانات التجريبية بنجاح</div>";
        
    } catch (Exception $e) {
        echo "<div style='color: red;'>✗ خطأ في إضافة البيانات: " . $e->getMessage() . "</div>";
    }
}

echo "<hr>";
echo "<h3>الخطوات التالية:</h3>";
echo "<ol>";
echo "<li>إذا كانت جميع الفحوصات ناجحة، جرب الرابط أعلاه</li>";
echo "<li>إذا لم يعمل الرابط، تحقق من ملف error.log في XAMPP</li>";
echo "<li>تأكد من أن Apache و MySQL يعملان</li>";
echo "</ol>";

echo "<p><a href='index.php'>العودة للصفحة الرئيسية</a></p>";
?>

<style>
body { font-family: 'Cairo', sans-serif; margin: 20px; }
h2, h3 { color: #333; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
