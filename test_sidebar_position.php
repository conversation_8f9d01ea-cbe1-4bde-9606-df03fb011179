<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

checkSession();
$user = getUserById($_SESSION['user_id']);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار موضع القائمة الجانبية</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">

    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }

        /* تخطيط القائمة على اليمين */
        @media (min-width: 768px) {
            .main-content-with-right-sidebar {
                margin-right: 300px;
                padding-right: 20px;
                padding-left: 20px;
            }
        }

        @media (max-width: 767.98px) {
            .main-content-with-right-sidebar {
                margin-right: 0;
                padding-right: 15px;
                padding-left: 15px;
            }
        }

        .test-content {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin: 20px 0;
        }

        .position-indicator {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,123,255,0.9);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            z-index: 1000;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <!-- Position Indicator -->
    <div class="position-indicator">
        <i class="fas fa-arrow-left me-2"></i>
        القائمة يجب أن تظهر على اليمين
        <i class="fas fa-arrow-right ms-2"></i>
    </div>

    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Main Content Area -->
            <main class="col-12 px-md-4 main-content-with-right-sidebar">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-test-tube me-2"></i>
                        اختبار موضع القائمة الجانبية
                    </h1>
                </div>

                <div class="test-content">
                    <h3>🎯 الهدف من الاختبار</h3>
                    <p>التأكد من أن القائمة الجانبية تظهر على <strong>اليمين</strong> بدلاً من اليسار.</p>

                    <h4>✅ ما يجب أن تراه:</h4>
                    <ul>
                        <li>القائمة الجانبية على <strong>الجانب الأيمن</strong> من الشاشة</li>
                        <li>المحتوى الرئيسي في <strong>الوسط والجانب الأيسر</strong></li>
                        <li>مساحة فارغة بين المحتوى والقائمة</li>
                    </ul>

                    <h4>❌ ما لا يجب أن تراه:</h4>
                    <ul>
                        <li>القائمة على الجانب الأيسر</li>
                        <li>تداخل بين المحتوى والقائمة</li>
                        <li>قائمة مخفية أو غير ظاهرة</li>
                    </ul>
                </div>

                <div class="test-content">
                    <h3>📱 اختبار الاستجابة</h3>
                    <p>جرب تغيير حجم النافذة لاختبار الاستجابة:</p>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h5>شاشة كبيرة</h5>
                                    <p>القائمة على اليمين</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h5>شاشة متوسطة</h5>
                                    <p>القائمة على اليمين</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h5>شاشة صغيرة</h5>
                                    <p>القائمة أعلى المحتوى</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="test-content">
                    <h3>🔗 اختبار الروابط</h3>
                    <p>جرب النقر على روابط القائمة للتأكد من عملها:</p>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="list-group">
                                <a href="index.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-home me-2"></i>
                                    الصفحة الرئيسية
                                </a>
                                <a href="branches.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-store me-2"></i>
                                    إدارة الفروع
                                </a>
                                <a href="pos.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-cash-register me-2"></i>
                                    نقاط البيع
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>ملاحظة</h6>
                                <p class="mb-0">يجب أن تعمل جميع الروابط بشكل صحيح مع القائمة في موضعها الجديد على اليمين.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="test-content">
                    <h3>📊 معلومات المستخدم</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <td><strong>اسم المستخدم:</strong></td>
                                    <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>نوع المستخدم:</strong></td>
                                    <td><?php echo htmlspecialchars($user['user_type']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>البريد الإلكتروني:</strong></td>
                                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check-circle me-2"></i>حالة النظام</h6>
                                <ul class="mb-0">
                                    <li>✅ تم تسجيل الدخول بنجاح</li>
                                    <li>✅ القائمة الجانبية محملة</li>
                                    <li>✅ التخطيط الجديد نشط</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <a href="index.php" class="btn btn-primary me-2">
                        <i class="fas fa-home me-2"></i>
                        العودة للصفحة الرئيسية
                    </a>
                    <a href="branches.php" class="btn btn-success me-2">
                        <i class="fas fa-store me-2"></i>
                        اختبار صفحة الفروع
                    </a>
                    <a href="pos.php" class="btn btn-info">
                        <i class="fas fa-cash-register me-2"></i>
                        اختبار صفحة نقاط البيع
                    </a>
                </div>
            </main>

            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // إخفاء مؤشر الموضع بعد 5 ثوان
        setTimeout(function() {
            const indicator = document.querySelector('.position-indicator');
            if (indicator) {
                indicator.style.opacity = '0';
                indicator.style.transition = 'opacity 1s ease';
                setTimeout(() => indicator.remove(), 1000);
            }
        }, 5000);

        // إضافة تأثير عند تحريك الماوس على القائمة
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.querySelector('.sidebar-right');
            if (sidebar) {
                sidebar.addEventListener('mouseenter', function() {
                    this.style.boxShadow = 'inset 3px 0 0 #007bff, 0 0 10px rgba(0,123,255,0.3)';
                });

                sidebar.addEventListener('mouseleave', function() {
                    this.style.boxShadow = 'inset 1px 0 0 rgba(0, 0, 0, .1)';
                });
            }
        });
    </script>
</body>
</html>
