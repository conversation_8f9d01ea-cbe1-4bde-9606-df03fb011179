<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

checkSession();

$user = getUserById($_SESSION['user_id']);
$branches = getAllBranches();

// فلترة البيانات
$branchFilter = $_GET['branch_id'] ?? '';
$posFilter = $_GET['pos_id'] ?? '';
$dateFrom = $_GET['date_from'] ?? date('Y-m-01');
$dateTo = $_GET['date_to'] ?? date('Y-m-d');
$statusFilter = $_GET['status'] ?? '';

// بناء استعلام البحث
$sql = "SELECT i.*, c.name as customer_name, b.name as branch_name, 
               p.name as pos_name, u.full_name as user_name 
        FROM invoices i 
        LEFT JOIN customers c ON i.customer_id = c.id 
        JOIN branches b ON i.branch_id = b.id 
        LEFT JOIN point_of_sales p ON i.pos_id = p.id 
        JOIN users u ON i.user_id = u.id 
        WHERE i.invoice_type = 'sale' 
        AND i.invoice_date BETWEEN ? AND ?";

$params = [$dateFrom, $dateTo];

if ($branchFilter) {
    $sql .= " AND i.branch_id = ?";
    $params[] = $branchFilter;
}

if ($posFilter) {
    $sql .= " AND i.pos_id = ?";
    $params[] = $posFilter;
}

if ($statusFilter) {
    $sql .= " AND i.status = ?";
    $params[] = $statusFilter;
}

// إضافة فلتر الفرع للمحاسب الفرعي
if ($user['user_type'] === 'branch_accountant' && $user['branch_id']) {
    $sql .= " AND i.branch_id = ?";
    $params[] = $user['branch_id'];
}

$sql .= " ORDER BY i.invoice_date DESC, i.created_at DESC";

$invoices = fetchAll($sql, $params);

// حساب الإجماليات
$totalAmount = array_sum(array_column($invoices, 'total_amount'));
$totalPaid = array_sum(array_column($invoices, 'paid_amount'));
$totalRemaining = array_sum(array_column($invoices, 'remaining_amount'));
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فواتير المبيعات - نظام المحاسبة المالية</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include '../../includes/navbar.php'; ?>
    
    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>
            
            <!-- Main Content Area -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-file-invoice-dollar me-2"></i>
                        فواتير المبيعات
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="add_sale.php" class="btn btn-primary">
                                <i class="fas fa-plus"></i> فاتورة جديدة
                            </a>
                            <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="exportToExcel()">
                                <i class="fas fa-file-excel"></i> تصدير
                            </button>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">إجمالي المبيعات</h6>
                                        <h4><?php echo number_format($totalAmount, 2); ?> د.أ</h4>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-dollar-sign fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">المبلغ المدفوع</h6>
                                        <h4><?php echo number_format($totalPaid, 2); ?> د.أ</h4>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-check-circle fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">المبلغ المتبقي</h6>
                                        <h4><?php echo number_format($totalRemaining, 2); ?> د.أ</h4>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-clock fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">عدد الفواتير</h6>
                                        <h4><?php echo count($invoices); ?></h4>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-file-invoice fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- فلاتر البحث -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2"></i>
                            فلاتر البحث
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="">
                            <div class="row">
                                <div class="col-md-3">
                                    <label for="date_from" class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from" 
                                           value="<?php echo htmlspecialchars($dateFrom); ?>">
                                </div>
                                <div class="col-md-3">
                                    <label for="date_to" class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to" 
                                           value="<?php echo htmlspecialchars($dateTo); ?>">
                                </div>
                                <?php if (isAdmin($_SESSION['user_id']) || isMainAccountant($_SESSION['user_id'])): ?>
                                <div class="col-md-2">
                                    <label for="branch_id" class="form-label">الفرع</label>
                                    <select class="form-select" id="branch_id" name="branch_id">
                                        <option value="">جميع الفروع</option>
                                        <?php foreach ($branches as $branch): ?>
                                        <option value="<?php echo $branch['id']; ?>" 
                                                <?php echo $branchFilter == $branch['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($branch['name']); ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <?php endif; ?>
                                <div class="col-md-2">
                                    <label for="status" class="form-label">الحالة</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="">جميع الحالات</option>
                                        <option value="draft" <?php echo $statusFilter == 'draft' ? 'selected' : ''; ?>>مسودة</option>
                                        <option value="pending" <?php echo $statusFilter == 'pending' ? 'selected' : ''; ?>>في الانتظار</option>
                                        <option value="approved" <?php echo $statusFilter == 'approved' ? 'selected' : ''; ?>>معتمدة</option>
                                        <option value="paid" <?php echo $statusFilter == 'paid' ? 'selected' : ''; ?>>مدفوعة</option>
                                        <option value="cancelled" <?php echo $statusFilter == 'cancelled' ? 'selected' : ''; ?>>ملغية</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i> بحث
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- جدول الفواتير -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة فواتير المبيعات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="invoicesTable">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>التاريخ</th>
                                        <th>العميل</th>
                                        <th>الفرع</th>
                                        <th>نقطة البيع</th>
                                        <th>المبلغ الإجمالي</th>
                                        <th>المبلغ المدفوع</th>
                                        <th>المبلغ المتبقي</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($invoices as $invoice): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($invoice['invoice_number']); ?></strong>
                                        </td>
                                        <td><?php echo date('Y-m-d', strtotime($invoice['invoice_date'])); ?></td>
                                        <td><?php echo htmlspecialchars($invoice['customer_name'] ?? 'عميل نقدي'); ?></td>
                                        <td><?php echo htmlspecialchars($invoice['branch_name']); ?></td>
                                        <td><?php echo htmlspecialchars($invoice['pos_name'] ?? '-'); ?></td>
                                        <td><?php echo number_format($invoice['total_amount'], 2); ?> د.أ</td>
                                        <td><?php echo number_format($invoice['paid_amount'], 2); ?> د.أ</td>
                                        <td><?php echo number_format($invoice['remaining_amount'], 2); ?> د.أ</td>
                                        <td>
                                            <?php
                                            $statusClass = '';
                                            $statusText = '';
                                            switch ($invoice['status']) {
                                                case 'draft':
                                                    $statusClass = 'bg-secondary';
                                                    $statusText = 'مسودة';
                                                    break;
                                                case 'pending':
                                                    $statusClass = 'bg-warning';
                                                    $statusText = 'في الانتظار';
                                                    break;
                                                case 'approved':
                                                    $statusClass = 'bg-info';
                                                    $statusText = 'معتمدة';
                                                    break;
                                                case 'paid':
                                                    $statusClass = 'bg-success';
                                                    $statusText = 'مدفوعة';
                                                    break;
                                                case 'cancelled':
                                                    $statusClass = 'bg-danger';
                                                    $statusText = 'ملغية';
                                                    break;
                                            }
                                            ?>
                                            <span class="badge <?php echo $statusClass; ?>">
                                                <?php echo $statusText; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="view_invoice.php?id=<?php echo $invoice['id']; ?>" 
                                                   class="btn btn-sm btn-outline-primary" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <?php if ($invoice['status'] !== 'cancelled'): ?>
                                                <a href="edit_invoice.php?id=<?php echo $invoice['id']; ?>" 
                                                   class="btn btn-sm btn-outline-secondary" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="print_invoice.php?id=<?php echo $invoice['id']; ?>" 
                                                   class="btn btn-sm btn-outline-info" title="طباعة" target="_blank">
                                                    <i class="fas fa-print"></i>
                                                </a>
                                                <?php if ($invoice['remaining_amount'] > 0): ?>
                                                <button type="button" class="btn btn-sm btn-outline-success" 
                                                        onclick="addPayment(<?php echo $invoice['id']; ?>)" title="إضافة دفعة">
                                                    <i class="fas fa-money-bill"></i>
                                                </button>
                                                <?php endif; ?>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- نافذة إضافة دفعة -->
    <div class="modal fade" id="paymentModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة دفعة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="paymentForm">
                    <div class="modal-body">
                        <input type="hidden" id="invoice_id" name="invoice_id">
                        
                        <div class="mb-3">
                            <label for="payment_amount" class="form-label">مبلغ الدفعة</label>
                            <input type="number" class="form-control" id="payment_amount" name="payment_amount" 
                                   step="0.01" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="payment_method" class="form-label">طريقة الدفع</label>
                            <select class="form-select" id="payment_method" name="payment_method" required>
                                <option value="cash">نقدي</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                                <option value="check">شيك</option>
                                <option value="credit_card">بطاقة ائتمان</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="payment_date" class="form-label">تاريخ الدفع</label>
                            <input type="date" class="form-control" id="payment_date" name="payment_date" 
                                   value="<?php echo date('Y-m-d'); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="reference_number" class="form-label">رقم المرجع</label>
                            <input type="text" class="form-control" id="reference_number" name="reference_number">
                        </div>
                        
                        <div class="mb-3">
                            <label for="payment_notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="payment_notes" name="payment_notes" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ الدفعة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // تهيئة DataTables
            $('#invoicesTable').DataTable({
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
                },
                order: [[1, 'desc']],
                pageLength: 25,
                responsive: true
            });
        });

        // إضافة دفعة
        function addPayment(invoiceId) {
            document.getElementById('invoice_id').value = invoiceId;
            const modal = new bootstrap.Modal(document.getElementById('paymentModal'));
            modal.show();
        }

        // معالجة نموذج الدفعة
        document.getElementById('paymentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch('../../api/add_payment.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم إضافة الدفعة بنجاح');
                    location.reload();
                } else {
                    alert('حدث خطأ: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال');
            });
        });

        // تصدير إلى Excel
        function exportToExcel() {
            const params = new URLSearchParams(window.location.search);
            params.set('export', 'excel');
            window.location.href = '../../api/export_sales.php?' + params.toString();
        }
    </script>
</body>
</html>
