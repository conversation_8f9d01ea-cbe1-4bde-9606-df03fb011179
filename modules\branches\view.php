<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

checkSession();

$user = getUserById($_SESSION['user_id']);

// التحقق من الصلاحيات
if (!isAdmin($_SESSION['user_id']) && !isMainAccountant($_SESSION['user_id'])) {
    header('Location: ../../index.php?error=access_denied');
    exit();
}

$branchId = $_GET['id'] ?? '';

if (empty($branchId)) {
    header('Location: list.php');
    exit();
}

// جلب بيانات الفرع
$sql = "SELECT b.*, c.name as company_name 
        FROM branches b 
        LEFT JOIN companies c ON b.company_id = c.id 
        WHERE b.id = ?";

$branch = fetchOne($sql, [$branchId]);

if (!$branch) {
    header('Location: list.php?error=branch_not_found');
    exit();
}

// جلب نقاط البيع للفرع
$posSql = "SELECT * FROM point_of_sales WHERE branch_id = ? AND is_active = 1 ORDER BY name";
$pointOfSales = fetchAll($posSql, [$branchId]);

// جلب المستخدمين للفرع
$usersSql = "SELECT * FROM users WHERE branch_id = ? AND is_active = 1 ORDER BY full_name";
$branchUsers = fetchAll($usersSql, [$branchId]);

// إحصائيات الفرع
$statsSql = "SELECT 
                COUNT(CASE WHEN invoice_type = 'sale' THEN 1 END) as sales_count,
                COUNT(CASE WHEN invoice_type = 'purchase' THEN 1 END) as purchases_count,
                COALESCE(SUM(CASE WHEN invoice_type = 'sale' THEN total_amount ELSE 0 END), 0) as total_sales,
                COALESCE(SUM(CASE WHEN invoice_type = 'purchase' THEN total_amount ELSE 0 END), 0) as total_purchases
             FROM invoices 
             WHERE branch_id = ? AND status != 'cancelled'";

$stats = fetchOne($statsSql, [$branchId]);

// أنواع نقاط البيع
$posTypes = [
    'supermarket' => 'سوبر ماركت',
    'cafeteria' => 'كافتيريا',
    'nuts_shop' => 'محمص',
    'sweets' => 'حلويات',
    'bakery' => 'مخبز'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض الفرع - <?php echo htmlspecialchars($branch['name']); ?></title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include '../../includes/navbar.php'; ?>
    
    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>
            
            <!-- Main Content Area -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-store me-2"></i>
                        <?php echo htmlspecialchars($branch['name']); ?>
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="list.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right"></i> العودة للقائمة
                            </a>
                            <?php if (isAdmin($_SESSION['user_id'])): ?>
                            <a href="edit.php?id=<?php echo $branch['id']; ?>" class="btn btn-primary">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            <?php endif; ?>
                            <button onclick="window.print()" class="btn btn-outline-info">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                        </div>
                    </div>
                </div>

                <!-- معلومات الفرع -->
                <div class="row mb-4">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    معلومات الفرع
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <table class="table table-borderless">
                                            <tr>
                                                <td><strong>كود الفرع:</strong></td>
                                                <td><span class="badge bg-primary fs-6"><?php echo htmlspecialchars($branch['code']); ?></span></td>
                                            </tr>
                                            <tr>
                                                <td><strong>اسم الفرع:</strong></td>
                                                <td><?php echo htmlspecialchars($branch['name']); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>الشركة:</strong></td>
                                                <td><?php echo htmlspecialchars($branch['company_name'] ?? 'غير محدد'); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>المدير:</strong></td>
                                                <td><?php echo htmlspecialchars($branch['manager_name'] ?? 'غير محدد'); ?></td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <table class="table table-borderless">
                                            <tr>
                                                <td><strong>الهاتف:</strong></td>
                                                <td><?php echo htmlspecialchars($branch['phone'] ?? 'غير محدد'); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>العنوان:</strong></td>
                                                <td><?php echo htmlspecialchars($branch['address'] ?? 'غير محدد'); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>تاريخ الإنشاء:</strong></td>
                                                <td><?php echo date('Y-m-d', strtotime($branch['created_at'])); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>الحالة:</strong></td>
                                                <td>
                                                    <?php if ($branch['is_active']): ?>
                                                    <span class="badge bg-success">نشط</span>
                                                    <?php else: ?>
                                                    <span class="badge bg-danger">غير نشط</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <!-- إحصائيات الفرع -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    إحصائيات الفرع
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="border-end">
                                            <h4 class="text-primary"><?php echo count($pointOfSales); ?></h4>
                                            <small>نقاط البيع</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <h4 class="text-success"><?php echo count($branchUsers); ?></h4>
                                        <small>المستخدمين</small>
                                    </div>
                                </div>
                                <hr>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="border-end">
                                            <h5 class="text-info"><?php echo number_format($stats['total_sales'], 0); ?></h5>
                                            <small>إجمالي المبيعات</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <h5 class="text-warning"><?php echo $stats['sales_count']; ?></h5>
                                        <small>عدد فواتير المبيعات</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- نقاط البيع -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-cash-register me-2"></i>
                            نقاط البيع (<?php echo count($pointOfSales); ?>)
                        </h5>
                        <?php if (isAdmin($_SESSION['user_id'])): ?>
                        <a href="../pos/add.php?branch_id=<?php echo $branch['id']; ?>" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> إضافة نقطة بيع
                        </a>
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <?php if (empty($pointOfSales)): ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-cash-register fa-3x mb-3"></i>
                            <p>لا توجد نقاط بيع في هذا الفرع</p>
                            <?php if (isAdmin($_SESSION['user_id'])): ?>
                            <a href="../pos/add.php?branch_id=<?php echo $branch['id']; ?>" class="btn btn-primary">
                                إضافة نقطة بيع جديدة
                            </a>
                            <?php endif; ?>
                        </div>
                        <?php else: ?>
                        <div class="row">
                            <?php foreach ($pointOfSales as $pos): ?>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title"><?php echo htmlspecialchars($pos['name']); ?></h6>
                                            <?php
                                            $typeIcons = [
                                                'supermarket' => 'fas fa-shopping-cart text-primary',
                                                'cafeteria' => 'fas fa-coffee text-warning',
                                                'nuts_shop' => 'fas fa-seedling text-success',
                                                'sweets' => 'fas fa-candy-cane text-danger',
                                                'bakery' => 'fas fa-bread-slice text-info'
                                            ];
                                            $icon = $typeIcons[$pos['type']] ?? 'fas fa-store';
                                            ?>
                                            <i class="<?php echo $icon; ?>"></i>
                                        </div>
                                        <p class="card-text">
                                            <small class="text-muted">
                                                <?php echo $posTypes[$pos['type']] ?? $pos['type']; ?>
                                            </small>
                                        </p>
                                        <?php if ($pos['description']): ?>
                                        <p class="card-text">
                                            <small><?php echo htmlspecialchars($pos['description']); ?></small>
                                        </p>
                                        <?php endif; ?>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="badge bg-<?php echo $pos['is_active'] ? 'success' : 'danger'; ?>">
                                                <?php echo $pos['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                            </span>
                                            <div class="btn-group" role="group">
                                                <a href="../pos/view.php?id=<?php echo $pos['id']; ?>" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <?php if (isAdmin($_SESSION['user_id'])): ?>
                                                <a href="../pos/edit.php?id=<?php echo $pos['id']; ?>" 
                                                   class="btn btn-sm btn-outline-secondary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- المستخدمين -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2"></i>
                            مستخدمي الفرع (<?php echo count($branchUsers); ?>)
                        </h5>
                        <?php if (isAdmin($_SESSION['user_id'])): ?>
                        <a href="../admin/add_user.php?branch_id=<?php echo $branch['id']; ?>" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> إضافة مستخدم
                        </a>
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <?php if (empty($branchUsers)): ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-users fa-3x mb-3"></i>
                            <p>لا يوجد مستخدمين مخصصين لهذا الفرع</p>
                        </div>
                        <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>اسم المستخدم</th>
                                        <th>النوع</th>
                                        <th>نقطة البيع</th>
                                        <th>آخر دخول</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($branchUsers as $branchUser): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($branchUser['full_name']); ?></td>
                                        <td><?php echo htmlspecialchars($branchUser['username']); ?></td>
                                        <td>
                                            <?php
                                            $userTypes = [
                                                'admin' => 'مدير النظام',
                                                'main_accountant' => 'محاسب رئيسي',
                                                'branch_accountant' => 'محاسب فرع',
                                                'cashier' => 'كاشير'
                                            ];
                                            echo $userTypes[$branchUser['user_type']] ?? $branchUser['user_type'];
                                            ?>
                                        </td>
                                        <td>
                                            <?php
                                            if ($branchUser['pos_id']) {
                                                $posName = fetchOne("SELECT name FROM point_of_sales WHERE id = ?", [$branchUser['pos_id']]);
                                                echo htmlspecialchars($posName['name'] ?? 'غير محدد');
                                            } else {
                                                echo 'جميع نقاط البيع';
                                            }
                                            ?>
                                        </td>
                                        <td>
                                            <?php 
                                            if ($branchUser['last_login']) {
                                                echo timeAgo($branchUser['last_login']);
                                            } else {
                                                echo 'لم يسجل دخول';
                                            }
                                            ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $branchUser['is_active'] ? 'success' : 'danger'; ?>">
                                                <?php echo $branchUser['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
