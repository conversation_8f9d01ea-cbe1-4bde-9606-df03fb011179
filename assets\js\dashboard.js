// نظام المحاسبة المالية - JavaScript للوحة التحكم

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة التطبيق
    initializeApp();
    
    // تحديث الوقت كل ثانية
    updateTime();
    setInterval(updateTime, 1000);
    
    // تحديث الإحصائيات كل 5 دقائق
    setInterval(updateStatistics, 300000);
    
    // تهيئة الأحداث
    initializeEvents();
});

// تهيئة التطبيق
function initializeApp() {
    // إضافة تأثيرات الحركة للبطاقات
    animateCards();
    
    // تهيئة التلميحات
    initializeTooltips();
    
    // تهيئة النوافذ المنبثقة
    initializeModals();
    
    // تحقق من الاتصال بالإنترنت
    checkConnection();
}

// تحديث الوقت
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleString('ar-JO', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    
    const timeElements = document.querySelectorAll('.current-time');
    timeElements.forEach(element => {
        element.textContent = timeString;
    });
}

// تحديث الإحصائيات
function updateStatistics() {
    fetch('api/get_statistics.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatisticsCards(data.statistics);
                updateCharts(data.charts);
            }
        })
        .catch(error => {
            console.error('خطأ في تحديث الإحصائيات:', error);
        });
}

// تحديث بطاقات الإحصائيات
function updateStatisticsCards(stats) {
    // تحديث إجمالي المبيعات
    const salesElement = document.querySelector('#total-sales');
    if (salesElement) {
        animateNumber(salesElement, stats.total_sales);
    }
    
    // تحديث إجمالي المشتريات
    const purchasesElement = document.querySelector('#total-purchases');
    if (purchasesElement) {
        animateNumber(purchasesElement, stats.total_purchases);
    }
    
    // تحديث عدد الفواتير
    const invoicesElement = document.querySelector('#total-invoices');
    if (invoicesElement) {
        animateNumber(invoicesElement, stats.total_invoices);
    }
    
    // تحديث صافي الربح
    const profitElement = document.querySelector('#net-profit');
    if (profitElement) {
        animateNumber(profitElement, stats.net_profit);
    }
}

// تحريك الأرقام
function animateNumber(element, targetValue) {
    const currentValue = parseFloat(element.textContent.replace(/[^\d.-]/g, '')) || 0;
    const increment = (targetValue - currentValue) / 20;
    let current = currentValue;
    
    const timer = setInterval(() => {
        current += increment;
        if ((increment > 0 && current >= targetValue) || (increment < 0 && current <= targetValue)) {
            current = targetValue;
            clearInterval(timer);
        }
        element.textContent = formatNumber(current);
    }, 50);
}

// تنسيق الأرقام
function formatNumber(number) {
    return new Intl.NumberFormat('ar-JO', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(number);
}

// تحديث الرسوم البيانية
function updateCharts(chartData) {
    // تحديث مخطط المبيعات
    if (window.salesChart && chartData.sales) {
        window.salesChart.data.datasets[0].data = chartData.sales;
        window.salesChart.update();
    }
    
    // تحديث مخطط الفروع
    if (window.branchChart && chartData.branches) {
        window.branchChart.data.datasets[0].data = chartData.branches;
        window.branchChart.update();
    }
}

// تحريك البطاقات
function animateCards() {
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// تهيئة التلميحات
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// تهيئة النوافذ المنبثقة
function initializeModals() {
    const modalTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="modal"]'));
    modalTriggerList.map(function (modalTriggerEl) {
        return new bootstrap.Modal(modalTriggerEl);
    });
}

// تهيئة الأحداث
function initializeEvents() {
    // حدث النقر على بطاقات الإحصائيات
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach(card => {
        card.addEventListener('click', function() {
            const link = this.dataset.link;
            if (link) {
                window.location.href = link;
            }
        });
    });
    
    // حدث تحديث البيانات
    const refreshButton = document.querySelector('#refresh-data');
    if (refreshButton) {
        refreshButton.addEventListener('click', function() {
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
            this.disabled = true;
            
            updateStatistics();
            
            setTimeout(() => {
                this.innerHTML = '<i class="fas fa-sync-alt"></i> تحديث';
                this.disabled = false;
            }, 2000);
        });
    }
    
    // حدث البحث السريع
    const quickSearch = document.querySelector('#quick-search');
    if (quickSearch) {
        quickSearch.addEventListener('input', function() {
            const query = this.value.toLowerCase();
            performQuickSearch(query);
        });
    }
    
    // حدث تصدير البيانات
    const exportButtons = document.querySelectorAll('.export-btn');
    exportButtons.forEach(button => {
        button.addEventListener('click', function() {
            const format = this.dataset.format;
            const type = this.dataset.type;
            exportData(format, type);
        });
    });
}

// البحث السريع
function performQuickSearch(query) {
    if (query.length < 2) {
        hideSearchResults();
        return;
    }
    
    fetch(`api/quick_search.php?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSearchResults(data.results);
            }
        })
        .catch(error => {
            console.error('خطأ في البحث:', error);
        });
}

// عرض نتائج البحث
function showSearchResults(results) {
    const resultsContainer = document.querySelector('#search-results');
    if (!resultsContainer) return;
    
    let html = '<div class="list-group">';
    
    results.forEach(result => {
        html += `
            <a href="${result.url}" class="list-group-item list-group-item-action">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">${result.title}</h6>
                    <small class="text-muted">${result.type}</small>
                </div>
                <p class="mb-1">${result.description}</p>
            </a>
        `;
    });
    
    html += '</div>';
    resultsContainer.innerHTML = html;
    resultsContainer.style.display = 'block';
}

// إخفاء نتائج البحث
function hideSearchResults() {
    const resultsContainer = document.querySelector('#search-results');
    if (resultsContainer) {
        resultsContainer.style.display = 'none';
    }
}

// تصدير البيانات
function exportData(format, type) {
    const url = `api/export.php?format=${format}&type=${type}`;
    
    // إنشاء رابط تحميل مؤقت
    const link = document.createElement('a');
    link.href = url;
    link.download = `${type}_${new Date().toISOString().split('T')[0]}.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// التحقق من الاتصال
function checkConnection() {
    if (!navigator.onLine) {
        showOfflineMessage();
    }
    
    window.addEventListener('online', hideOfflineMessage);
    window.addEventListener('offline', showOfflineMessage);
}

// عرض رسالة عدم الاتصال
function showOfflineMessage() {
    const message = document.createElement('div');
    message.id = 'offline-message';
    message.className = 'alert alert-warning position-fixed top-0 start-50 translate-middle-x mt-3';
    message.style.zIndex = '9999';
    message.innerHTML = `
        <i class="fas fa-wifi me-2"></i>
        لا يوجد اتصال بالإنترنت. بعض الميزات قد لا تعمل بشكل صحيح.
    `;
    
    document.body.appendChild(message);
}

// إخفاء رسالة عدم الاتصال
function hideOfflineMessage() {
    const message = document.querySelector('#offline-message');
    if (message) {
        message.remove();
    }
}

// وظائف مساعدة للرسوم البيانية
function createSalesChart(ctx, data) {
    return new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
            datasets: [{
                label: 'المبيعات',
                data: data,
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#007bff',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#007bff',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: false,
                    callbacks: {
                        label: function(context) {
                            return `المبيعات: ${formatNumber(context.parsed.y)} دينار`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        font: {
                            family: 'Cairo'
                        },
                        callback: function(value) {
                            return formatNumber(value);
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });
}

function createBranchChart(ctx, labels, data) {
    return new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF',
                    '#FF9F40'
                ],
                borderWidth: 0,
                hoverBorderWidth: 3,
                hoverBorderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        font: {
                            family: 'Cairo',
                            size: 12
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#007bff',
                    borderWidth: 1,
                    cornerRadius: 8,
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = formatNumber(context.parsed);
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return `${label}: ${value} دينار (${percentage}%)`;
                        }
                    }
                }
            },
            cutout: '60%'
        }
    });
}

// وظائف التحقق من صحة البيانات
function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return false;
    
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            showFieldError(input, 'هذا الحقل مطلوب');
            isValid = false;
        } else {
            hideFieldError(input);
        }
    });
    
    return isValid;
}

function showFieldError(field, message) {
    hideFieldError(field);
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback d-block';
    errorDiv.textContent = message;
    
    field.classList.add('is-invalid');
    field.parentNode.appendChild(errorDiv);
}

function hideFieldError(field) {
    field.classList.remove('is-invalid');
    const errorDiv = field.parentNode.querySelector('.invalid-feedback');
    if (errorDiv) {
        errorDiv.remove();
    }
}

// وظائف الإشعارات
function showNotification(message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // إزالة الإشعار تلقائياً
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, duration);
}

// وظائف التحميل
function showLoading(element) {
    const originalContent = element.innerHTML;
    element.dataset.originalContent = originalContent;
    element.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
    element.disabled = true;
}

function hideLoading(element) {
    const originalContent = element.dataset.originalContent;
    if (originalContent) {
        element.innerHTML = originalContent;
        delete element.dataset.originalContent;
    }
    element.disabled = false;
}

// تصدير الوظائف للاستخدام العام
window.AccountingSystem = {
    updateStatistics,
    showNotification,
    validateForm,
    showLoading,
    hideLoading,
    formatNumber,
    createSalesChart,
    createBranchChart
};
