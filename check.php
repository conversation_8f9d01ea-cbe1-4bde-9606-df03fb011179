<?php
// فحص حالة النظام
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص النظام - نظام المحاسبة المالية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: #f8f9fa; }
        .check-item { padding: 10px; margin: 5px 0; border-radius: 5px; }
        .check-pass { background: #d4edda; color: #155724; }
        .check-fail { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-check-circle"></i> فحص حالة النظام</h3>
                    </div>
                    <div class="card-body">
                        <?php
                        echo '<h5>معلومات PHP:</h5>';
                        echo '<div class="check-item check-pass">إصدار PHP: ' . PHP_VERSION . '</div>';
                        
                        echo '<h5 class="mt-4">الإضافات المطلوبة:</h5>';
                        
                        $extensions = ['pdo', 'pdo_mysql', 'json', 'mbstring', 'openssl'];
                        foreach ($extensions as $ext) {
                            $loaded = extension_loaded($ext);
                            $class = $loaded ? 'check-pass' : 'check-fail';
                            $status = $loaded ? '✓ متوفر' : '✗ غير متوفر';
                            echo "<div class='check-item $class'>$ext: $status</div>";
                        }
                        
                        echo '<h5 class="mt-4">الملفات والمجلدات:</h5>';
                        
                        $files = [
                            'config/database.php' => 'ملف إعدادات قاعدة البيانات',
                            'includes/functions.php' => 'ملف الوظائف',
                            'database/schema.sql' => 'ملف هيكل قاعدة البيانات',
                            'install.php' => 'معالج التثبيت'
                        ];
                        
                        foreach ($files as $file => $desc) {
                            $exists = file_exists($file);
                            $class = $exists ? 'check-pass' : 'check-fail';
                            $status = $exists ? '✓ موجود' : '✗ غير موجود';
                            echo "<div class='check-item $class'>$desc ($file): $status</div>";
                        }
                        
                        $dirs = ['uploads', 'backups', 'assets', 'modules'];
                        foreach ($dirs as $dir) {
                            $exists = is_dir($dir);
                            $writable = $exists && is_writable($dir);
                            $class = $exists && $writable ? 'check-pass' : 'check-fail';
                            $status = $exists ? ($writable ? '✓ موجود وقابل للكتابة' : '⚠ موجود لكن غير قابل للكتابة') : '✗ غير موجود';
                            echo "<div class='check-item $class'>مجلد $dir: $status</div>";
                        }
                        
                        echo '<h5 class="mt-4">اختبار قاعدة البيانات:</h5>';
                        
                        try {
                            if (file_exists('config/database.php')) {
                                require_once 'config/database.php';
                                echo '<div class="check-item check-pass">✓ تم تحميل إعدادات قاعدة البيانات بنجاح</div>';
                                
                                if (isset($pdo)) {
                                    echo '<div class="check-item check-pass">✓ تم الاتصال بقاعدة البيانات بنجاح</div>';
                                } else {
                                    echo '<div class="check-item check-fail">✗ فشل في الاتصال بقاعدة البيانات</div>';
                                }
                            } else {
                                echo '<div class="check-item check-fail">✗ ملف إعدادات قاعدة البيانات غير موجود</div>';
                            }
                        } catch (Exception $e) {
                            echo '<div class="check-item check-fail">✗ خطأ في قاعدة البيانات: ' . htmlspecialchars($e->getMessage()) . '</div>';
                        }
                        ?>
                        
                        <div class="mt-4">
                            <h5>الخطوات التالية:</h5>
                            <div class="alert alert-info">
                                <?php if (!file_exists('config/database.php')): ?>
                                <p><strong>1.</strong> قم بتشغيل معالج التثبيت: <a href="install.php" class="btn btn-primary btn-sm">تشغيل التثبيت</a></p>
                                <?php else: ?>
                                <p><strong>1.</strong> النظام جاهز للاستخدام: <a href="index.php" class="btn btn-success btn-sm">الذهاب للنظام</a></p>
                                <?php endif; ?>
                                <p><strong>2.</strong> إذا واجهت مشاكل، تحقق من ملف error.log في مجلد Apache</p>
                                <p><strong>3.</strong> تأكد من تشغيل XAMPP وتفعيل Apache و MySQL</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
