<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

checkSession();
$user = getUserById($_SESSION['user_id']);

// التحقق من الصلاحيات
if (!isAdmin($_SESSION['user_id']) && !isMainAccountant($_SESSION['user_id'])) {
    header('Location: index.php?error=access_denied');
    exit();
}

// معالجة الحذف
if (isset($_GET['delete']) && isAdmin($_SESSION['user_id'])) {
    $branchId = $_GET['delete'];
    try {
        executeQuery("UPDATE branches SET is_active = 0 WHERE id = ?", [$branchId]);
        $success = 'تم حذف الفرع بنجاح';
        logActivity($_SESSION['user_id'], 'حذف فرع', "تم حذف الفرع رقم $branchId");
    } catch (Exception $e) {
        $error = 'حدث خطأ في حذف الفرع';
    }
}

// جلب الفروع
try {
    $sql = "SELECT b.*, c.name as company_name,
                   (SELECT COUNT(*) FROM point_of_sales WHERE branch_id = b.id AND is_active = 1) as pos_count,
                   (SELECT COUNT(*) FROM users WHERE branch_id = b.id AND is_active = 1) as users_count
            FROM branches b
            LEFT JOIN companies c ON b.company_id = c.id
            WHERE b.is_active = 1
            ORDER BY b.name";

    $branches = fetchAll($sql);
} catch (Exception $e) {
    $branches = [];
    $error = 'خطأ في جلب بيانات الفروع: ' . $e->getMessage();
}

// جلب نقاط البيع
try {
    $posSql = "SELECT p.*, b.name as branch_name, b.code as branch_code
               FROM point_of_sales p
               JOIN branches b ON p.branch_id = b.id
               WHERE p.is_active = 1
               ORDER BY b.name, p.name";

    $pointOfSales = fetchAll($posSql);
} catch (Exception $e) {
    $pointOfSales = [];
}

// أنواع نقاط البيع
$posTypes = [
    'supermarket' => 'سوبر ماركت',
    'cafeteria' => 'كافتيريا',
    'nuts_shop' => 'محمص',
    'sweets' => 'حلويات',
    'bakery' => 'مخبز'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الفروع ونقاط البيع - نظام المحاسبة المالية</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">

    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body { font-family: 'Cairo', sans-serif; }
        .card { margin-bottom: 20px; }
        .table th { background-color: #f8f9fa; }
        .badge { font-size: 0.8em; }
        .btn-group .btn { margin: 0 2px; }

        /* تخطيط القائمة على اليمين */
        @media (min-width: 768px) {
            .main-content-with-right-sidebar {
                margin-right: 300px;
                padding-right: 20px;
                padding-left: 20px;
            }
        }

        @media (max-width: 767.98px) {
            .main-content-with-right-sidebar {
                margin-right: 0;
                padding-right: 15px;
                padding-left: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Main Content Area -->
            <main class="col-12 px-md-4 main-content-with-right-sidebar">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-store me-2"></i>
                        إدارة الفروع ونقاط البيع
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <?php if (isAdmin($_SESSION['user_id'])): ?>
                            <a href="add_branch.php" class="btn btn-primary">
                                <i class="fas fa-plus"></i> إضافة فرع
                            </a>
                            <?php endif; ?>
                            <a href="index.php" class="btn btn-outline-secondary">
                                <i class="fas fa-home"></i> الصفحة الرئيسية
                            </a>
                            <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                        </div>
                    </div>
                </div>

                <?php if (isset($success)): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if (isset($error)): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h4><?php echo count($branches); ?></h4>
                                <p>إجمالي الفروع</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h4><?php echo count($pointOfSales); ?></h4>
                                <p>نقاط البيع</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h4><?php echo array_sum(array_column($branches, 'users_count')); ?></h4>
                                <p>المستخدمين</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h4><?php echo count(array_filter($branches, function($b) { return $b['is_active']; })); ?></h4>
                                <p>الفروع النشطة</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول الفروع -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة الفروع (<?php echo count($branches); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($branches)): ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-store fa-3x mb-3"></i>
                            <p>لا توجد فروع في النظام</p>
                            <?php if (isAdmin($_SESSION['user_id'])): ?>
                            <a href="add_branch.php" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                إضافة فرع جديد
                            </a>
                            <?php endif; ?>
                        </div>
                        <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>كود الفرع</th>
                                        <th>اسم الفرع</th>
                                        <th>العنوان</th>
                                        <th>الهاتف</th>
                                        <th>المدير</th>
                                        <th>نقاط البيع</th>
                                        <th>المستخدمين</th>
                                        <th>الحالة</th>
                                        <?php if (isAdmin($_SESSION['user_id'])): ?>
                                        <th>الإجراءات</th>
                                        <?php endif; ?>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($branches as $branch): ?>
                                    <tr>
                                        <td>
                                            <strong class="text-primary"><?php echo htmlspecialchars($branch['code']); ?></strong>
                                        </td>
                                        <td>
                                            <div>
                                                <strong><?php echo htmlspecialchars($branch['name']); ?></strong>
                                                <?php if ($branch['company_name']): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($branch['company_name']); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <small><?php echo htmlspecialchars($branch['address'] ?? '-'); ?></small>
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars($branch['phone'] ?? '-'); ?>
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars($branch['manager_name'] ?? '-'); ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $branch['pos_count']; ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success"><?php echo $branch['users_count']; ?></span>
                                        </td>
                                        <td>
                                            <?php if ($branch['is_active']): ?>
                                            <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                            <span class="badge bg-danger">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <?php if (isAdmin($_SESSION['user_id'])): ?>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary"
                                                        onclick="viewBranch(<?php echo $branch['id']; ?>)" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary"
                                                        onclick="editBranch(<?php echo $branch['id']; ?>)" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger"
                                                        onclick="confirmDelete(<?php echo $branch['id']; ?>, '<?php echo htmlspecialchars($branch['name']); ?>')"
                                                        title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                        <?php endif; ?>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- جدول نقاط البيع -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cash-register me-2"></i>
                            قائمة نقاط البيع (<?php echo count($pointOfSales); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($pointOfSales)): ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-cash-register fa-3x mb-3"></i>
                            <p>لا توجد نقاط بيع في النظام</p>
                            <?php if (isAdmin($_SESSION['user_id'])): ?>
                            <button class="btn btn-primary" onclick="addPOS()">
                                إضافة نقطة بيع جديدة
                            </button>
                            <?php endif; ?>
                        </div>
                        <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>اسم نقطة البيع</th>
                                        <th>الفرع</th>
                                        <th>النوع</th>
                                        <th>الوصف</th>
                                        <th>الحالة</th>
                                        <?php if (isAdmin($_SESSION['user_id'])): ?>
                                        <th>الإجراءات</th>
                                        <?php endif; ?>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($pointOfSales as $pos): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($pos['name']); ?></strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?php echo htmlspecialchars($pos['branch_name']); ?></span>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($pos['branch_code']); ?></small>
                                        </td>
                                        <td>
                                            <?php
                                            $typeIcons = [
                                                'supermarket' => 'fas fa-shopping-cart text-primary',
                                                'cafeteria' => 'fas fa-coffee text-warning',
                                                'nuts_shop' => 'fas fa-seedling text-success',
                                                'sweets' => 'fas fa-candy-cane text-danger',
                                                'bakery' => 'fas fa-bread-slice text-info'
                                            ];
                                            $icon = $typeIcons[$pos['type']] ?? 'fas fa-store';
                                            ?>
                                            <i class="<?php echo $icon; ?> me-2"></i>
                                            <?php echo $posTypes[$pos['type']] ?? $pos['type']; ?>
                                        </td>
                                        <td>
                                            <small><?php echo htmlspecialchars($pos['description'] ?? '-'); ?></small>
                                        </td>
                                        <td>
                                            <?php if ($pos['is_active']): ?>
                                            <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                            <span class="badge bg-danger">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <?php if (isAdmin($_SESSION['user_id'])): ?>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary"
                                                        onclick="viewPOS(<?php echo $pos['id']; ?>)" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary"
                                                        onclick="editPOS(<?php echo $pos['id']; ?>)" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger"
                                                        onclick="confirmDeletePOS(<?php echo $pos['id']; ?>, '<?php echo htmlspecialchars($pos['name']); ?>')"
                                                        title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                        <?php endif; ?>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>

            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // تأكيد الحذف
        function confirmDelete(branchId, branchName) {
            if (confirm('هل تريد حذف أو إلغاء تفعيل الفرع "' + branchName + '"؟\n\nسيتم توجيهك إلى صفحة خيارات الحذف.')) {
                window.location.href = 'delete_branch.php?id=' + branchId;
            }
        }

        function confirmDeletePOS(posId, posName) {
            if (confirm('هل أنت متأكد من حذف نقطة البيع "' + posName + '"؟')) {
                window.location.href = '?delete_pos=' + posId;
            }
        }

        // وظائف إدارة الفروع
        function viewBranch(id) {
            window.location.href = 'view_branch.php?id=' + id;
        }

        function editBranch(id) {
            window.location.href = 'edit_branch.php?id=' + id;
        }

        function addBranch() {
            window.location.href = 'add_branch.php';
        }

        function viewPOS(id) {
            alert('عرض تفاصيل نقطة البيع رقم: ' + id);
        }

        function editPOS(id) {
            alert('تعديل نقطة البيع رقم: ' + id);
        }

        function addPOS() {
            alert('إضافة نقطة بيع جديدة');
        }
    </script>
</body>
</html>
