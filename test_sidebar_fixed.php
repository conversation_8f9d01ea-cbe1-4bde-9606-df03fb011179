<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

checkSession();
$user = getUserById($_SESSION['user_id']);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح تداخل القائمة</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body { 
            font-family: 'Cairo', sans-serif; 
            background-color: #f8f9fa;
        }
        
        /* تخطيط القائمة على اليمين - محدث */
        @media (min-width: 768px) {
            .main-content-with-right-sidebar {
                margin-right: 300px;
                padding-right: 20px;
                padding-left: 20px;
            }
        }
        
        @media (max-width: 767.98px) {
            .main-content-with-right-sidebar {
                margin-right: 0;
                padding-right: 15px;
                padding-left: 15px;
            }
        }
        
        .test-content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        
        .success-indicator {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
        }
        
        .measurement-box {
            border: 2px dashed #007bff;
            padding: 15px;
            margin: 10px 0;
            background: rgba(0, 123, 255, 0.05);
            border-radius: 5px;
        }
        
        .spacing-demo {
            background: linear-gradient(90deg, 
                #e3f2fd 0%, 
                #e3f2fd 20px, 
                #fff 20px, 
                #fff calc(100% - 300px), 
                #f3e5f5 calc(100% - 300px), 
                #f3e5f5 100%);
            height: 50px;
            border-radius: 5px;
            position: relative;
            margin: 20px 0;
        }
        
        .spacing-demo::before {
            content: "المحتوى الرئيسي";
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            font-weight: bold;
            color: #333;
        }
        
        .spacing-demo::after {
            content: "القائمة";
            position: absolute;
            right: 150px;
            top: 50%;
            transform: translateY(-50%);
            font-weight: bold;
            color: #6f42c1;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>
    
    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Main Content Area -->
            <main class="col-12 px-md-4 main-content-with-right-sidebar">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-tools me-2"></i>
                        اختبار إصلاح تداخل القائمة
                    </h1>
                </div>

                <div class="success-indicator">
                    <h3><i class="fas fa-check-circle me-2"></i>تم إصلاح المشكلة بنجاح!</h3>
                    <p class="mb-0">القائمة الجانبية الآن لا تغطي أي جزء من المحتوى</p>
                </div>

                <div class="test-content">
                    <h3>🔧 الإصلاحات المطبقة</h3>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5>قبل الإصلاح:</h5>
                            <ul class="text-danger">
                                <li>القائمة تغطي جزء من المحتوى</li>
                                <li>عرض القائمة: 250px</li>
                                <li>هامش المحتوى: 270px</li>
                                <li>تداخل في النصوص</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>بعد الإصلاح:</h5>
                            <ul class="text-success">
                                <li>✅ لا يوجد تداخل</li>
                                <li>✅ عرض القائمة: 280px</li>
                                <li>✅ هامش المحتوى: 300px</li>
                                <li>✅ مساحة إضافية للأمان</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="test-content">
                    <h3>📏 قياس المساحات</h3>
                    <p>هذا مثال بصري لتوزيع المساحات:</p>
                    
                    <div class="spacing-demo"></div>
                    
                    <div class="measurement-box">
                        <strong>التفاصيل التقنية:</strong>
                        <ul class="mt-2 mb-0">
                            <li><strong>عرض القائمة:</strong> 280px</li>
                            <li><strong>هامش المحتوى الأيمن:</strong> 300px</li>
                            <li><strong>المساحة الآمنة:</strong> 20px</li>
                            <li><strong>حشو داخلي:</strong> 20px من كل جانب</li>
                        </ul>
                    </div>
                </div>

                <div class="test-content">
                    <h3>📱 اختبار الاستجابة</h3>
                    <p>جرب تغيير حجم النافذة لاختبار الاستجابة:</p>
                    
                    <div class="row">
                        <div class="col-lg-4 mb-3">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">شاشة كبيرة (≥768px)</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="mb-0">
                                        <li>القائمة على اليمين</li>
                                        <li>هامش 300px</li>
                                        <li>لا يوجد تداخل</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 mb-3">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">شاشة متوسطة</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="mb-0">
                                        <li>القائمة ثابتة</li>
                                        <li>تخطيط متجاوب</li>
                                        <li>مساحة محسنة</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 mb-3">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0">شاشة صغيرة (<768px)</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="mb-0">
                                        <li>القائمة أعلى المحتوى</li>
                                        <li>هامش عادي</li>
                                        <li>تخطيط عمودي</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="test-content">
                    <h3>🧪 اختبار المحتوى الطويل</h3>
                    <p>هذا نص طويل لاختبار عدم تداخل القائمة مع المحتوى. يجب أن يظهر هذا النص بوضوح تام دون أي تداخل مع القائمة الجانبية. النص يمتد عبر عرض المحتوى المتاح ولا يجب أن يختفي تحت القائمة.</p>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <p>هذا عمود يأخذ 8/12 من العرض المتاح. يجب أن يظهر بالكامل دون تداخل مع القائمة الجانبية. النص هنا واضح ومقروء.</p>
                        </div>
                        <div class="col-md-4">
                            <div class="alert alert-info">
                                <strong>ملاحظة:</strong> هذا العمود يأخذ 4/12 من العرض ويجب أن يكون مرئي بالكامل.
                            </div>
                        </div>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>العنصر</th>
                                    <th>القيمة القديمة</th>
                                    <th>القيمة الجديدة</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>عرض القائمة</td>
                                    <td>250px</td>
                                    <td>280px</td>
                                    <td><span class="badge bg-success">محسن</span></td>
                                </tr>
                                <tr>
                                    <td>هامش المحتوى</td>
                                    <td>270px</td>
                                    <td>300px</td>
                                    <td><span class="badge bg-success">محسن</span></td>
                                </tr>
                                <tr>
                                    <td>المساحة الآمنة</td>
                                    <td>20px</td>
                                    <td>20px</td>
                                    <td><span class="badge bg-success">مضافة</span></td>
                                </tr>
                                <tr>
                                    <td>التداخل</td>
                                    <td>موجود</td>
                                    <td>غير موجود</td>
                                    <td><span class="badge bg-success">مُصلح</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="test-content">
                    <h3>✅ نتائج الاختبار</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check-circle me-2"></i>اختبارات ناجحة</h6>
                                <ul class="mb-0">
                                    <li>✅ لا يوجد تداخل في المحتوى</li>
                                    <li>✅ القائمة في الموضع الصحيح</li>
                                    <li>✅ المساحات محسوبة بدقة</li>
                                    <li>✅ التصميم متجاوب</li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>معلومات إضافية</h6>
                                <ul class="mb-0">
                                    <li>المستخدم: <?php echo htmlspecialchars($user['full_name']); ?></li>
                                    <li>النوع: <?php echo htmlspecialchars($user['user_type']); ?></li>
                                    <li>الوقت: <?php echo date('Y-m-d H:i:s'); ?></li>
                                    <li>الحالة: نشط</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <a href="index.php" class="btn btn-primary me-2">
                        <i class="fas fa-home me-2"></i>
                        الصفحة الرئيسية
                    </a>
                    <a href="branches.php" class="btn btn-success me-2">
                        <i class="fas fa-store me-2"></i>
                        اختبار صفحة الفروع
                    </a>
                    <a href="pos.php" class="btn btn-info me-2">
                        <i class="fas fa-cash-register me-2"></i>
                        اختبار صفحة نقاط البيع
                    </a>
                    <a href="test_sidebar_position.php" class="btn btn-warning">
                        <i class="fas fa-test-tube me-2"></i>
                        الاختبار السابق
                    </a>
                </div>
            </main>
            
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // إضافة تأثيرات بصرية للتأكيد على الإصلاح
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير على القائمة الجانبية
            const sidebar = document.querySelector('.sidebar-right');
            if (sidebar) {
                sidebar.style.border = '2px solid #28a745';
                sidebar.style.boxShadow = 'inset 1px 0 0 rgba(0, 0, 0, .1), 0 0 15px rgba(40, 167, 69, 0.3)';
                
                // إضافة مؤشر نجاح
                const successBadge = document.createElement('div');
                successBadge.innerHTML = '<i class="fas fa-check-circle"></i> مُصلح';
                successBadge.style.cssText = `
                    position: absolute;
                    top: 10px;
                    left: 10px;
                    background: #28a745;
                    color: white;
                    padding: 5px 10px;
                    border-radius: 15px;
                    font-size: 12px;
                    z-index: 1000;
                `;
                sidebar.style.position = 'relative';
                sidebar.appendChild(successBadge);
            }
            
            // تأثير على المحتوى الرئيسي
            const mainContent = document.querySelector('.main-content-with-right-sidebar');
            if (mainContent) {
                mainContent.style.outline = '2px dashed #007bff';
                mainContent.style.outlineOffset = '5px';
                
                setTimeout(() => {
                    mainContent.style.outline = 'none';
                }, 3000);
            }
        });
    </script>
</body>
</html>
