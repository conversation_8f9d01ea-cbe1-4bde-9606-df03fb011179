<?php
// اختبار الاتصال بقاعدة البيانات الجديدة
echo "<h2>اختبار الاتصال بقاعدة البيانات</h2>";

$host = "localhost";
$username = "md";
$password = "mm\$\$12345dd";
$database = "md3";

echo "<p><strong>بيانات الاتصال:</strong></p>";
echo "<ul>";
echo "<li>الخادم: $host</li>";
echo "<li>المستخدم: $username</li>";
echo "<li>كلمة المرور: " . str_repeat('*', strlen($password)) . "</li>";
echo "<li>قاعدة البيانات: $database</li>";
echo "</ul>";

try {
    // محاولة الاتصال بدون تحديد قاعدة البيانات أولاً
    echo "<h3>1. اختبار الاتصال بالخادم:</h3>";
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ تم الاتصال بالخادم بنجاح!</p>";

    // فحص وجود قاعدة البيانات
    echo "<h3>2. فحص وجود قاعدة البيانات:</h3>";
    $stmt = $pdo->query("SHOW DATABASES LIKE '$database'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ قاعدة البيانات '$database' موجودة!</p>";

        // الاتصال بقاعدة البيانات
        echo "<h3>3. الاتصال بقاعدة البيانات:</h3>";
        $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "<p style='color: green;'>✓ تم الاتصال بقاعدة البيانات بنجاح!</p>";

        // فحص الجداول
        echo "<h3>4. فحص الجداول:</h3>";
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

        if (count($tables) > 0) {
            echo "<p style='color: green;'>✓ تم العثور على " . count($tables) . " جدول:</p>";
            echo "<ul>";
            foreach ($tables as $table) {
                echo "<li>$table</li>";
            }
            echo "</ul>";

            // فحص جدول المستخدمين
            if (in_array('users', $tables)) {
                echo "<h3>5. فحص المستخدمين:</h3>";
                $stmt = $pdo->query("SELECT username, user_type FROM users");
                $users = $stmt->fetchAll();

                if (count($users) > 0) {
                    echo "<p style='color: green;'>✓ تم العثور على " . count($users) . " مستخدم:</p>";
                    echo "<ul>";
                    foreach ($users as $user) {
                        echo "<li>{$user['username']} ({$user['user_type']})</li>";
                    }
                    echo "</ul>";
                } else {
                    echo "<p style='color: orange;'>⚠ لا توجد مستخدمين في قاعدة البيانات</p>";
                }
            } else {
                echo "<p style='color: orange;'>⚠ جدول المستخدمين غير موجود</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠ قاعدة البيانات فارغة - لا توجد جداول</p>";
        }

    } else {
        echo "<p style='color: orange;'>⚠ قاعدة البيانات '$database' غير موجودة</p>";

        // إنشاء قاعدة البيانات
        echo "<h3>3. إنشاء قاعدة البيانات:</h3>";
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "<p style='color: green;'>✓ تم إنشاء قاعدة البيانات '$database' بنجاح!</p>";
    }

} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ خطأ في الاتصال: " . $e->getMessage() . "</p>";

    // اقتراحات لحل المشكلة
    echo "<h3>اقتراحات لحل المشكلة:</h3>";
    echo "<ul>";
    echo "<li>تأكد من تشغيل MySQL في XAMPP</li>";
    echo "<li>تحقق من صحة اسم المستخدم وكلمة المرور</li>";
    echo "<li>تأكد من أن المستخدم 'md' له صلاحيات الوصول</li>";
    echo "<li>جرب استخدام 'root' كاسم مستخدم إذا لم يعمل 'md'</li>";
    echo "</ul>";
}

echo "<hr>";
echo "<h3>الخطوات التالية:</h3>";
echo "<ul>";
echo "<li><a href='install.php'>تشغيل معالج التثبيت</a></li>";
echo "<li><a href='debug.php'>تشخيص شامل للنظام</a></li>";
echo "<li><a href='welcome.php'>العودة للصفحة الرئيسية</a></li>";
echo "</ul>";
?>
