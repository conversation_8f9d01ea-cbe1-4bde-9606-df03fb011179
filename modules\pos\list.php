<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

checkSession();

$user = getUserById($_SESSION['user_id']);

// التحقق من الصلاحيات
if (!isAdmin($_SESSION['user_id']) && !isMainAccountant($_SESSION['user_id'])) {
    header('Location: ../../index.php?error=access_denied');
    exit();
}

// فلتر الفرع
$branchFilter = $_GET['branch_id'] ?? '';
$branches = getAllBranches();

// معالجة الحذف
if (isset($_GET['delete']) && isAdmin($_SESSION['user_id'])) {
    $posId = $_GET['delete'];
    try {
        executeQuery("UPDATE point_of_sales SET is_active = 0 WHERE id = ?", [$posId]);
        $success = 'تم حذف نقطة البيع بنجاح';
        logActivity($_SESSION['user_id'], 'حذف نقطة بيع', "تم حذف نقطة البيع رقم $posId");
    } catch (Exception $e) {
        $error = 'حدث خطأ في حذف نقطة البيع';
    }
}

// جلب نقاط البيع
$sql = "SELECT p.*, b.name as branch_name, b.code as branch_code,
               (SELECT COUNT(*) FROM users WHERE pos_id = p.id AND is_active = 1) as users_count
        FROM point_of_sales p 
        JOIN branches b ON p.branch_id = b.id 
        WHERE p.is_active = 1";

$params = [];

if ($branchFilter) {
    $sql .= " AND p.branch_id = ?";
    $params[] = $branchFilter;
}

// فلتر الفرع للمحاسب الفرعي
if ($user['user_type'] === 'branch_accountant' && $user['branch_id']) {
    $sql .= " AND p.branch_id = ?";
    $params[] = $user['branch_id'];
}

$sql .= " ORDER BY b.name, p.name";

$pointOfSales = fetchAll($sql, $params);

// أنواع نقاط البيع
$posTypes = [
    'supermarket' => 'سوبر ماركت',
    'cafeteria' => 'كافتيريا',
    'nuts_shop' => 'محمص',
    'sweets' => 'حلويات',
    'bakery' => 'مخبز'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة نقاط البيع - نظام المحاسبة المالية</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include '../../includes/navbar.php'; ?>
    
    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>
            
            <!-- Main Content Area -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-cash-register me-2"></i>
                        إدارة نقاط البيع
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <?php if (isAdmin($_SESSION['user_id'])): ?>
                            <a href="add.php" class="btn btn-primary">
                                <i class="fas fa-plus"></i> إضافة نقطة بيع
                            </a>
                            <?php endif; ?>
                            <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                        </div>
                    </div>
                </div>

                <?php if (isset($success)): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if (isset($error)): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- فلاتر البحث -->
                <?php if (isAdmin($_SESSION['user_id']) || isMainAccountant($_SESSION['user_id'])): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2"></i>
                            فلاتر البحث
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="">
                            <div class="row">
                                <div class="col-md-4">
                                    <label for="branch_id" class="form-label">الفرع</label>
                                    <select class="form-select" id="branch_id" name="branch_id">
                                        <option value="">جميع الفروع</option>
                                        <?php foreach ($branches as $branch): ?>
                                        <option value="<?php echo $branch['id']; ?>" 
                                                <?php echo $branchFilter == $branch['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($branch['name']); ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i> بحث
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <?php endif; ?>

                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <?php
                    $typeStats = [];
                    foreach ($pointOfSales as $pos) {
                        $typeStats[$pos['type']] = ($typeStats[$pos['type']] ?? 0) + 1;
                    }
                    ?>
                    <div class="col-md-2">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h4><?php echo count($pointOfSales); ?></h4>
                                <small>إجمالي نقاط البيع</small>
                            </div>
                        </div>
                    </div>
                    <?php foreach ($posTypes as $type => $typeName): ?>
                    <div class="col-md-2">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h4><?php echo $typeStats[$type] ?? 0; ?></h4>
                                <small><?php echo $typeName; ?></small>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- جدول نقاط البيع -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة نقاط البيع
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="posTable">
                                <thead>
                                    <tr>
                                        <th>اسم نقطة البيع</th>
                                        <th>الفرع</th>
                                        <th>النوع</th>
                                        <th>الوصف</th>
                                        <th>المستخدمين</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($pointOfSales as $pos): ?>
                                    <tr>
                                        <td>
                                            <div>
                                                <strong><?php echo htmlspecialchars($pos['name']); ?></strong>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($pos['branch_code']); ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?php echo htmlspecialchars($pos['branch_name']); ?></span>
                                        </td>
                                        <td>
                                            <?php
                                            $typeIcons = [
                                                'supermarket' => 'fas fa-shopping-cart',
                                                'cafeteria' => 'fas fa-coffee',
                                                'nuts_shop' => 'fas fa-seedling',
                                                'sweets' => 'fas fa-candy-cane',
                                                'bakery' => 'fas fa-bread-slice'
                                            ];
                                            $icon = $typeIcons[$pos['type']] ?? 'fas fa-store';
                                            ?>
                                            <i class="<?php echo $icon; ?> me-2"></i>
                                            <?php echo $posTypes[$pos['type']] ?? $pos['type']; ?>
                                        </td>
                                        <td>
                                            <small><?php echo htmlspecialchars($pos['description'] ?? '-'); ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-success"><?php echo $pos['users_count']; ?></span>
                                        </td>
                                        <td>
                                            <?php if ($pos['is_active']): ?>
                                            <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                            <span class="badge bg-danger">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="view.php?id=<?php echo $pos['id']; ?>" 
                                                   class="btn btn-sm btn-outline-primary" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <?php if (isAdmin($_SESSION['user_id'])): ?>
                                                <a href="edit.php?id=<?php echo $pos['id']; ?>" 
                                                   class="btn btn-sm btn-outline-secondary" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        onclick="confirmDelete(<?php echo $pos['id']; ?>, '<?php echo htmlspecialchars($pos['name']); ?>')" 
                                                        title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // تهيئة DataTables
            $('#posTable').DataTable({
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
                },
                order: [[1, 'asc'], [0, 'asc']],
                pageLength: 25,
                responsive: true
            });
        });

        // تأكيد الحذف
        function confirmDelete(posId, posName) {
            if (confirm('هل أنت متأكد من حذف نقطة البيع "' + posName + '"؟\n\nسيتم إلغاء تفعيل نقطة البيع وليس حذفها نهائياً.')) {
                window.location.href = '?delete=' + posId;
            }
        }
    </script>
</body>
</html>
