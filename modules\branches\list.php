<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

checkSession();

$user = getUserById($_SESSION['user_id']);

// التحقق من الصلاحيات
if (!isAdmin($_SESSION['user_id']) && !isMainAccountant($_SESSION['user_id'])) {
    header('Location: ../../index.php?error=access_denied');
    exit();
}

// معالجة الحذف
if (isset($_GET['delete']) && isAdmin($_SESSION['user_id'])) {
    $branchId = $_GET['delete'];
    try {
        executeQuery("UPDATE branches SET is_active = 0 WHERE id = ?", [$branchId]);
        $success = 'تم حذف الفرع بنجاح';
        logActivity($_SESSION['user_id'], 'حذف فرع', "تم حذف الفرع رقم $branchId");
    } catch (Exception $e) {
        $error = 'حدث خطأ في حذف الفرع';
    }
}

// جلب الفروع
$sql = "SELECT b.*, c.name as company_name,
               (SELECT COUNT(*) FROM point_of_sales WHERE branch_id = b.id AND is_active = 1) as pos_count,
               (SELECT COUNT(*) FROM users WHERE branch_id = b.id AND is_active = 1) as users_count
        FROM branches b 
        LEFT JOIN companies c ON b.company_id = c.id 
        WHERE b.is_active = 1 
        ORDER BY b.name";

$branches = fetchAll($sql);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الفروع - نظام المحاسبة المالية</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include '../../includes/navbar.php'; ?>
    
    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>
            
            <!-- Main Content Area -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-store me-2"></i>
                        إدارة الفروع
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <?php if (isAdmin($_SESSION['user_id'])): ?>
                            <a href="add.php" class="btn btn-primary">
                                <i class="fas fa-plus"></i> إضافة فرع جديد
                            </a>
                            <?php endif; ?>
                            <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="exportToExcel()">
                                <i class="fas fa-file-excel"></i> تصدير
                            </button>
                        </div>
                    </div>
                </div>

                <?php if (isset($success)): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if (isset($error)): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">إجمالي الفروع</h6>
                                        <h4><?php echo count($branches); ?></h4>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-store fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">نقاط البيع</h6>
                                        <h4><?php echo array_sum(array_column($branches, 'pos_count')); ?></h4>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-cash-register fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">المستخدمين</h6>
                                        <h4><?php echo array_sum(array_column($branches, 'users_count')); ?></h4>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">الفروع النشطة</h6>
                                        <h4><?php echo count(array_filter($branches, function($b) { return $b['is_active']; })); ?></h4>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-check-circle fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول الفروع -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة الفروع
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="branchesTable">
                                <thead>
                                    <tr>
                                        <th>كود الفرع</th>
                                        <th>اسم الفرع</th>
                                        <th>العنوان</th>
                                        <th>الهاتف</th>
                                        <th>المدير</th>
                                        <th>نقاط البيع</th>
                                        <th>المستخدمين</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($branches as $branch): ?>
                                    <tr>
                                        <td>
                                            <strong class="text-primary"><?php echo htmlspecialchars($branch['code']); ?></strong>
                                        </td>
                                        <td>
                                            <div>
                                                <strong><?php echo htmlspecialchars($branch['name']); ?></strong>
                                                <?php if ($branch['company_name']): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($branch['company_name']); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <small><?php echo htmlspecialchars($branch['address'] ?? '-'); ?></small>
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars($branch['phone'] ?? '-'); ?>
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars($branch['manager_name'] ?? '-'); ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $branch['pos_count']; ?></span>
                                            <a href="../pos/list.php?branch_id=<?php echo $branch['id']; ?>" class="btn btn-sm btn-outline-info ms-1">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                        <td>
                                            <span class="badge bg-success"><?php echo $branch['users_count']; ?></span>
                                        </td>
                                        <td>
                                            <?php if ($branch['is_active']): ?>
                                            <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                            <span class="badge bg-danger">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="view.php?id=<?php echo $branch['id']; ?>" 
                                                   class="btn btn-sm btn-outline-primary" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <?php if (isAdmin($_SESSION['user_id'])): ?>
                                                <a href="edit.php?id=<?php echo $branch['id']; ?>" 
                                                   class="btn btn-sm btn-outline-secondary" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        onclick="confirmDelete(<?php echo $branch['id']; ?>, '<?php echo htmlspecialchars($branch['name']); ?>')" 
                                                        title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // تهيئة DataTables
            $('#branchesTable').DataTable({
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
                },
                order: [[1, 'asc']],
                pageLength: 25,
                responsive: true
            });
        });

        // تأكيد الحذف
        function confirmDelete(branchId, branchName) {
            if (confirm('هل أنت متأكد من حذف الفرع "' + branchName + '"؟\n\nسيتم إلغاء تفعيل الفرع وليس حذفه نهائياً.')) {
                window.location.href = '?delete=' + branchId;
            }
        }

        // تصدير إلى Excel
        function exportToExcel() {
            window.location.href = '../../api/export_branches.php';
        }
    </script>
</body>
</html>
