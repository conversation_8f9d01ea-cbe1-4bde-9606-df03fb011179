<?php
session_start();

// التحقق من وجود ملف الإعدادات
if (!file_exists('config/database.php')) {
    header('Location: install.php');
    exit();
}

try {
    require_once 'config/database.php';
    require_once 'includes/functions.php';
} catch (Exception $e) {
    header('Location: install.php');
    exit();
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = trim($_POST['username']);
    $password = $_POST['password'];

    if (empty($username) || empty($password)) {
        $error = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        try {
            $user = authenticateUser($username, $password);
            if ($user) {
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['user_type'] = $user['user_type'];
                $_SESSION['branch_id'] = $user['branch_id'];

                // تسجيل عملية تسجيل الدخول
                try {
                    logActivity($user['id'], 'تسجيل دخول', 'تم تسجيل الدخول بنجاح');
                } catch (Exception $e) {
                    // تجاهل خطأ تسجيل النشاط
                }

                header('Location: index.php');
                exit();
            } else {
                $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
            }
        } catch (Exception $e) {
            $error = 'خطأ في قاعدة البيانات. يرجى التأكد من تثبيت النظام أولاً.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام المحاسبة المالية</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }

        .login-form {
            padding: 60px 40px;
        }

        .login-image {
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            padding: 60px 40px;
        }

        .form-control {
            border: none;
            border-bottom: 2px solid #e0e0e0;
            border-radius: 0;
            padding: 15px 0;
            background: transparent;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-bottom-color: #667eea;
            box-shadow: none;
            background: transparent;
        }

        .btn-login {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 50px;
            padding: 15px 40px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .logo {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #667eea;
        }

        .system-title {
            color: #333;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .system-subtitle {
            color: #666;
            margin-bottom: 40px;
        }

        .feature-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.8;
        }

        .alert {
            border-radius: 10px;
            border: none;
        }

        .input-group {
            margin-bottom: 25px;
        }

        .input-group-text {
            background: transparent;
            border: none;
            border-bottom: 2px solid #e0e0e0;
            border-radius: 0;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="row g-0">
                <!-- Login Form -->
                <div class="col-lg-6">
                    <div class="login-form">
                        <div class="text-center mb-4">
                            <i class="fas fa-calculator logo"></i>
                            <h2 class="system-title">نظام المحاسبة المالية</h2>
                            <p class="system-subtitle">نظام متكامل لإدارة الحسابات والفروع</p>
                        </div>

                        <?php if ($error): ?>
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" action="">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-user"></i>
                                </span>
                                <input type="text" class="form-control" name="username"
                                       placeholder="اسم المستخدم" required>
                            </div>

                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input type="password" class="form-control" name="password"
                                       placeholder="كلمة المرور" required>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-login">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    تسجيل الدخول
                                </button>
                            </div>
                        </form>

                        <div class="text-center mt-4">
                            <small class="text-muted">
                                <i class="fas fa-shield-alt me-1"></i>
                                نظام آمن ومحمي
                            </small>
                            <br>
                            <small class="text-muted mt-2">
                                لم يتم تثبيت النظام بعد؟
                                <a href="install.php" class="text-primary">اضغط هنا للتثبيت</a>
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Login Image/Info -->
                <div class="col-lg-6">
                    <div class="login-image">
                        <div class="text-center">
                            <i class="fas fa-chart-line feature-icon"></i>
                            <h3 class="mb-4">مميزات النظام</h3>
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <i class="fas fa-store me-2"></i>
                                    إدارة متعددة الفروع
                                </div>
                                <div class="col-12 mb-3">
                                    <i class="fas fa-file-invoice me-2"></i>
                                    فواتير المشتريات والمبيعات
                                </div>
                                <div class="col-12 mb-3">
                                    <i class="fas fa-chart-pie me-2"></i>
                                    تقارير مالية شاملة
                                </div>
                                <div class="col-12 mb-3">
                                    <i class="fas fa-users me-2"></i>
                                    نظام صلاحيات متقدم
                                </div>
                                <div class="col-12 mb-3">
                                    <i class="fas fa-mobile-alt me-2"></i>
                                    واجهة متجاوبة وحديثة
                                </div>
                                <div class="col-12">
                                    <i class="fas fa-lock me-2"></i>
                                    حماية وأمان عالي
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // تأثيرات بصرية للنموذج
        document.addEventListener('DOMContentLoaded', function() {
            const inputs = document.querySelectorAll('.form-control');

            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });

                input.addEventListener('blur', function() {
                    if (this.value === '') {
                        this.parentElement.classList.remove('focused');
                    }
                });
            });
        });
    </script>
</body>
</html>
