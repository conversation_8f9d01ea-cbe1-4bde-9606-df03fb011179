<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

echo "<h1>اختبار صفحة الفروع البسيط</h1>";

// التحقق من الجلسة
if (!isset($_SESSION['user_id'])) {
    echo "<p style='color: red;'>خطأ: لم يتم تسجيل الدخول</p>";
    echo "<a href='../../login.php'>تسجيل الدخول</a>";
    exit();
}

echo "<p style='color: green;'>✓ تم تسجيل الدخول بنجاح</p>";

// جلب بيانات المستخدم
try {
    $user = getUserById($_SESSION['user_id']);
    echo "<p>المستخدم: " . htmlspecialchars($user['full_name']) . "</p>";
    echo "<p>نوع المستخدم: " . htmlspecialchars($user['user_type']) . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ في جلب بيانات المستخدم: " . $e->getMessage() . "</p>";
}

// التحقق من الصلاحيات
if (!isAdmin($_SESSION['user_id']) && !isMainAccountant($_SESSION['user_id'])) {
    echo "<p style='color: red;'>خطأ: ليس لديك صلاحية للوصول لهذه الصفحة</p>";
    echo "<a href='../../index.php'>العودة للصفحة الرئيسية</a>";
    exit();
}

echo "<p style='color: green;'>✓ لديك صلاحية الوصول</p>";

// جلب الفروع
try {
    $sql = "SELECT * FROM branches WHERE is_active = 1 ORDER BY name";
    $branches = fetchAll($sql);
    
    echo "<h2>قائمة الفروع (" . count($branches) . ")</h2>";
    
    if (empty($branches)) {
        echo "<p>لا توجد فروع في النظام</p>";
        echo "<p><a href='add.php'>إضافة فرع جديد</a></p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 10px;'>كود الفرع</th>";
        echo "<th style='padding: 10px;'>اسم الفرع</th>";
        echo "<th style='padding: 10px;'>العنوان</th>";
        echo "<th style='padding: 10px;'>الهاتف</th>";
        echo "<th style='padding: 10px;'>المدير</th>";
        echo "<th style='padding: 10px;'>الإجراءات</th>";
        echo "</tr>";
        
        foreach ($branches as $branch) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($branch['code']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($branch['name']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($branch['address'] ?? '-') . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($branch['phone'] ?? '-') . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($branch['manager_name'] ?? '-') . "</td>";
            echo "<td style='padding: 8px;'>";
            echo "<a href='view.php?id=" . $branch['id'] . "'>عرض</a> | ";
            echo "<a href='edit.php?id=" . $branch['id'] . "'>تعديل</a>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ في جلب الفروع: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>روابط مفيدة:</h3>";
echo "<ul>";
echo "<li><a href='list.php'>الصفحة الكاملة للفروع</a></li>";
echo "<li><a href='add.php'>إضافة فرع جديد</a></li>";
echo "<li><a href='../pos/list.php'>قائمة نقاط البيع</a></li>";
echo "<li><a href='../../index.php'>العودة للصفحة الرئيسية</a></li>";
echo "</ul>";
?>

<style>
body { 
    font-family: 'Cairo', sans-serif; 
    margin: 20px; 
    direction: rtl;
}
h1, h2, h3 { color: #333; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
table { margin-top: 20px; }
</style>
