<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

checkSession();

$user = getUserById($_SESSION['user_id']);

// التحقق من الصلاحيات
if (!isAdmin($_SESSION['user_id'])) {
    header('Location: ../../index.php?error=access_denied');
    exit();
}

$error = '';
$success = '';

// جلب الشركات
$companies = fetchAll("SELECT * FROM companies ORDER BY name");

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $companyId = $_POST['company_id'] ?? '';
        $name = trim($_POST['name']);
        $code = trim($_POST['code']);
        $address = trim($_POST['address']);
        $phone = trim($_POST['phone']);
        $managerName = trim($_POST['manager_name']);
        
        // التحقق من صحة البيانات
        if (empty($name) || empty($code)) {
            throw new Exception('يرجى ملء جميع الحقول المطلوبة');
        }
        
        // التحقق من عدم تكرار الكود
        $existingBranch = fetchOne("SELECT id FROM branches WHERE code = ?", [$code]);
        if ($existingBranch) {
            throw new Exception('كود الفرع موجود مسبقاً');
        }
        
        // إدراج الفرع الجديد
        $sql = "INSERT INTO branches (company_id, name, code, address, phone, manager_name) 
                VALUES (?, ?, ?, ?, ?, ?)";
        
        executeQuery($sql, [$companyId ?: null, $name, $code, $address, $phone, $managerName]);
        
        $branchId = getLastInsertId();
        
        // تسجيل النشاط
        logActivity($_SESSION['user_id'], 'إضافة فرع', "تم إضافة فرع جديد: $name ($code)");
        
        $success = 'تم إضافة الفرع بنجاح';
        
        // إعادة توجيه إلى صفحة القائمة
        header("Location: list.php?success=1");
        exit();
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة فرع جديد - نظام المحاسبة المالية</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include '../../includes/navbar.php'; ?>
    
    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>
            
            <!-- Main Content Area -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-plus me-2"></i>
                        إضافة فرع جديد
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="list.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right"></i> العودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>

                <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
                <?php endif; ?>

                <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
                <?php endif; ?>

                <!-- نموذج إضافة الفرع -->
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    بيانات الفرع
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" id="branchForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="company_id" class="form-label">الشركة</label>
                                                <select class="form-select" id="company_id" name="company_id">
                                                    <option value="">اختر الشركة (اختياري)</option>
                                                    <?php foreach ($companies as $company): ?>
                                                    <option value="<?php echo $company['id']; ?>">
                                                        <?php echo htmlspecialchars($company['name']); ?>
                                                    </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="code" class="form-label">كود الفرع *</label>
                                                <input type="text" class="form-control" id="code" name="code" 
                                                       placeholder="مثال: ZRQ001" required>
                                                <div class="form-text">كود فريد للفرع (مثال: ZRQ001)</div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="name" class="form-label">اسم الفرع *</label>
                                        <input type="text" class="form-control" id="name" name="name" 
                                               placeholder="مثال: فرع الزرقاء" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="address" class="form-label">العنوان</label>
                                        <textarea class="form-control" id="address" name="address" rows="3" 
                                                  placeholder="العنوان التفصيلي للفرع"></textarea>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="phone" class="form-label">رقم الهاتف</label>
                                                <input type="tel" class="form-control" id="phone" name="phone" 
                                                       placeholder="+962-6-1234567">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="manager_name" class="form-label">اسم المدير</label>
                                                <input type="text" class="form-control" id="manager_name" name="manager_name" 
                                                       placeholder="اسم مدير الفرع">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                            <i class="fas fa-undo me-2"></i>
                                            إعادة تعيين
                                        </button>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>
                                            حفظ الفرع
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-lightbulb me-2"></i>
                                    نصائح
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة</h6>
                                    <ul class="mb-0">
                                        <li>كود الفرع يجب أن يكون فريداً</li>
                                        <li>يُفضل استخدام أكواد قصيرة ومفهومة</li>
                                        <li>يمكن إضافة نقاط البيع بعد إنشاء الفرع</li>
                                        <li>جميع الحقول المطلوبة مميزة بـ *</li>
                                    </ul>
                                </div>
                                
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيه</h6>
                                    <p class="mb-0">تأكد من صحة البيانات قبل الحفظ، حيث أن بعض البيانات لا يمكن تعديلها لاحقاً.</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-list me-2"></i>
                                    الخطوات التالية
                                </h5>
                            </div>
                            <div class="card-body">
                                <ol>
                                    <li>إنشاء الفرع</li>
                                    <li>إضافة نقاط البيع للفرع</li>
                                    <li>إنشاء مستخدمين للفرع</li>
                                    <li>تحديد الصلاحيات</li>
                                    <li>بدء العمل</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // إعادة تعيين النموذج
        function resetForm() {
            if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟ سيتم فقدان جميع البيانات المدخلة.')) {
                document.getElementById('branchForm').reset();
            }
        }
        
        // التحقق من صحة النموذج
        document.getElementById('branchForm').addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const code = document.getElementById('code').value.trim();
            
            if (!name || !code) {
                e.preventDefault();
                alert('يرجى ملء جميع الحقول المطلوبة');
                return false;
            }
            
            // التحقق من تنسيق الكود
            if (!/^[A-Z0-9]{3,10}$/.test(code)) {
                e.preventDefault();
                alert('كود الفرع يجب أن يحتوي على أحرف إنجليزية كبيرة وأرقام فقط (3-10 أحرف)');
                return false;
            }
        });
        
        // تحويل كود الفرع إلى أحرف كبيرة تلقائياً
        document.getElementById('code').addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });
    </script>
</body>
</html>
