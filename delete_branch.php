<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

checkSession();

// التحقق من الصلاحيات
if (!isAdmin($_SESSION['user_id'])) {
    header('Location: index.php?error=access_denied');
    exit();
}

$branchId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$branchId) {
    header('Location: branches.php?error=invalid_id');
    exit();
}

$error = '';
$success = '';

// جلب بيانات الفرع
try {
    $branch = fetchOne("SELECT * FROM branches WHERE id = ?", [$branchId]);
    if (!$branch) {
        header('Location: branches.php?error=branch_not_found');
        exit();
    }
} catch (Exception $e) {
    header('Location: branches.php?error=database_error');
    exit();
}

// التحقق من وجود بيانات مرتبطة
$hasRelatedData = false;
$relatedDataInfo = [];

try {
    // فحص نقاط البيع
    $posCount = fetchOne("SELECT COUNT(*) as count FROM point_of_sales WHERE branch_id = ?", [$branchId])['count'];
    if ($posCount > 0) {
        $hasRelatedData = true;
        $relatedDataInfo[] = "نقاط البيع: $posCount";
    }
    
    // فحص الفواتير
    $invoicesCount = fetchOne("SELECT COUNT(*) as count FROM invoices WHERE branch_id = ?", [$branchId])['count'];
    if ($invoicesCount > 0) {
        $hasRelatedData = true;
        $relatedDataInfo[] = "الفواتير: $invoicesCount";
    }
    
    // فحص المستخدمين
    $usersCount = fetchOne("SELECT COUNT(*) as count FROM users WHERE branch_id = ?", [$branchId])['count'];
    if ($usersCount > 0) {
        $hasRelatedData = true;
        $relatedDataInfo[] = "المستخدمين: $usersCount";
    }
    
    // فحص المخزون
    $inventoryCount = fetchOne("SELECT COUNT(*) as count FROM inventory WHERE branch_id = ?", [$branchId])['count'];
    if ($inventoryCount > 0) {
        $hasRelatedData = true;
        $relatedDataInfo[] = "عناصر المخزون: $inventoryCount";
    }
    
} catch (Exception $e) {
    $error = 'خطأ في فحص البيانات المرتبطة';
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'soft_delete') {
        // حذف ناعم - إلغاء تفعيل الفرع
        try {
            executeQuery("UPDATE branches SET is_active = 0, updated_at = NOW() WHERE id = ?", [$branchId]);
            
            // إلغاء تفعيل نقاط البيع التابعة
            executeQuery("UPDATE point_of_sales SET is_active = 0 WHERE branch_id = ?", [$branchId]);
            
            logActivity($_SESSION['user_id'], 'إلغاء تفعيل فرع', "تم إلغاء تفعيل الفرع: {$branch['name']} ({$branch['code']})");
            
            header('Location: branches.php?success=' . urlencode('تم إلغاء تفعيل الفرع بنجاح'));
            exit();
            
        } catch (Exception $e) {
            $error = 'خطأ في إلغاء تفعيل الفرع: ' . $e->getMessage();
        }
        
    } elseif ($action === 'hard_delete' && $_POST['confirm_code'] === $branch['code']) {
        // حذف نهائي
        try {
            // بدء المعاملة
            beginTransaction();
            
            // حذف البيانات المرتبطة بالترتيب الصحيح
            executeQuery("DELETE FROM inventory WHERE branch_id = ?", [$branchId]);
            executeQuery("DELETE FROM invoice_items WHERE invoice_id IN (SELECT id FROM invoices WHERE branch_id = ?)", [$branchId]);
            executeQuery("DELETE FROM invoices WHERE branch_id = ?", [$branchId]);
            executeQuery("DELETE FROM point_of_sales WHERE branch_id = ?", [$branchId]);
            executeQuery("UPDATE users SET branch_id = NULL WHERE branch_id = ?", [$branchId]);
            executeQuery("DELETE FROM activity_logs WHERE description LIKE ?", ["%{$branch['code']}%"]);
            
            // حذف الفرع نفسه
            executeQuery("DELETE FROM branches WHERE id = ?", [$branchId]);
            
            // تأكيد المعاملة
            commitTransaction();
            
            logActivity($_SESSION['user_id'], 'حذف فرع', "تم حذف الفرع نهائياً: {$branch['name']} ({$branch['code']})");
            
            header('Location: branches.php?success=' . urlencode('تم حذف الفرع نهائياً'));
            exit();
            
        } catch (Exception $e) {
            // إلغاء المعاملة في حالة الخطأ
            rollbackTransaction();
            $error = 'خطأ في حذف الفرع: ' . $e->getMessage();
        }
        
    } elseif ($action === 'hard_delete') {
        $error = 'كود التأكيد غير صحيح';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حذف الفرع - نظام المحاسبة المالية</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body { font-family: 'Cairo', sans-serif; }
        
        /* تخطيط القائمة على اليمين */
        @media (min-width: 768px) {
            .main-content-with-right-sidebar {
                margin-right: 300px;
                padding-right: 20px;
                padding-left: 20px;
            }
        }
        
        @media (max-width: 767.98px) {
            .main-content-with-right-sidebar {
                margin-right: 0;
                padding-right: 15px;
                padding-left: 15px;
            }
        }
        
        .danger-zone {
            border: 2px solid #dc3545;
            border-radius: 10px;
            background: #fff5f5;
        }
        
        .warning-zone {
            border: 2px solid #ffc107;
            border-radius: 10px;
            background: #fffbf0;
        }
        
        .branch-info {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            border-radius: 10px;
            padding: 20px;
        }
        
        .related-data-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
        }
        
        .confirm-input {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>
    
    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Main Content Area -->
            <main class="col-12 px-md-4 main-content-with-right-sidebar">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2 text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        حذف الفرع
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="branches.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right"></i> العودة للقائمة
                            </a>
                            <a href="view_branch.php?id=<?php echo $branchId; ?>" class="btn btn-outline-info">
                                <i class="fas fa-eye"></i> عرض التفاصيل
                            </a>
                        </div>
                    </div>
                </div>

                <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- معلومات الفرع -->
                <div class="branch-info mb-4">
                    <h3 class="mb-3">
                        <i class="fas fa-store me-2"></i>
                        الفرع المراد حذفه
                    </h3>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>الاسم:</strong> <?php echo htmlspecialchars($branch['name']); ?></p>
                            <p><strong>الكود:</strong> <?php echo htmlspecialchars($branch['code']); ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>تاريخ الإنشاء:</strong> <?php echo date('Y-m-d', strtotime($branch['created_at'])); ?></p>
                            <p><strong>الحالة:</strong> 
                                <?php if ($branch['is_active']): ?>
                                <span class="badge bg-light text-dark">نشط</span>
                                <?php else: ?>
                                <span class="badge bg-light text-dark">غير نشط</span>
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- الحذف الناعم -->
                    <div class="col-lg-6 mb-4">
                        <div class="warning-zone p-4">
                            <h4 class="text-warning mb-3">
                                <i class="fas fa-pause-circle me-2"></i>
                                إلغاء تفعيل الفرع (مُوصى به)
                            </h4>
                            
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-info-circle me-2"></i>ماذا سيحدث؟</h6>
                                <ul class="mb-0">
                                    <li>سيتم إلغاء تفعيل الفرع</li>
                                    <li>سيتم إلغاء تفعيل جميع نقاط البيع التابعة</li>
                                    <li>لن يظهر الفرع في القوائم النشطة</li>
                                    <li>ستبقى جميع البيانات محفوظة</li>
                                    <li>يمكن إعادة تفعيله لاحقاً</li>
                                </ul>
                            </div>
                            
                            <form method="POST" class="mt-3">
                                <input type="hidden" name="action" value="soft_delete">
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-warning btn-lg" onclick="return confirm('هل أنت متأكد من إلغاء تفعيل هذا الفرع؟')">
                                        <i class="fas fa-pause me-2"></i>
                                        إلغاء تفعيل الفرع
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- الحذف النهائي -->
                    <div class="col-lg-6 mb-4">
                        <div class="danger-zone p-4">
                            <h4 class="text-danger mb-3">
                                <i class="fas fa-trash me-2"></i>
                                حذف نهائي (خطير جداً!)
                            </h4>
                            
                            <div class="alert alert-danger">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>تحذير!</h6>
                                <p class="mb-2">هذا الإجراء سيحذف نهائياً:</p>
                                <ul class="mb-0">
                                    <li>الفرع وجميع بياناته</li>
                                    <li>جميع نقاط البيع التابعة</li>
                                    <li>جميع الفواتير والمعاملات</li>
                                    <li>جميع بيانات المخزون</li>
                                    <li>سجلات الأنشطة المرتبطة</li>
                                </ul>
                                <p class="mt-2 mb-0"><strong>لا يمكن التراجع عن هذا الإجراء!</strong></p>
                            </div>

                            <?php if ($hasRelatedData): ?>
                            <div class="alert alert-info">
                                <h6><i class="fas fa-database me-2"></i>البيانات المرتبطة</h6>
                                <?php foreach ($relatedDataInfo as $info): ?>
                                <div class="related-data-item">
                                    <i class="fas fa-link me-2"></i>
                                    <?php echo $info; ?>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <?php endif; ?>
                            
                            <form method="POST" id="deleteForm" class="mt-3">
                                <input type="hidden" name="action" value="hard_delete">
                                
                                <div class="mb-3">
                                    <label for="confirm_code" class="form-label">
                                        <strong>للتأكيد، اكتب كود الفرع:</strong>
                                        <code class="text-danger"><?php echo htmlspecialchars($branch['code']); ?></code>
                                    </label>
                                    <input type="text" class="form-control confirm-input" id="confirm_code" name="confirm_code" 
                                           placeholder="اكتب كود الفرع هنا" required>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="understand_risk" required>
                                    <label class="form-check-label text-danger" for="understand_risk">
                                        <strong>أفهم أن هذا الإجراء لا يمكن التراجع عنه</strong>
                                    </label>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-danger btn-lg" id="deleteButton" disabled>
                                        <i class="fas fa-trash me-2"></i>
                                        حذف نهائي - لا يمكن التراجع
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-question-circle me-2"></i>
                            أيهما أختار؟
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-warning">إلغاء التفعيل مناسب عندما:</h6>
                                <ul>
                                    <li>تريد إيقاف الفرع مؤقتاً</li>
                                    <li>تريد الاحتفاظ بالبيانات للمراجعة</li>
                                    <li>قد تحتاج لإعادة تفعيل الفرع لاحقاً</li>
                                    <li>تريد الاحتفاظ بالتقارير والإحصائيات</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-danger">الحذف النهائي مناسب عندما:</h6>
                                <ul>
                                    <li>تريد إزالة الفرع نهائياً</li>
                                    <li>لا تحتاج للبيانات مطلقاً</li>
                                    <li>تريد توفير مساحة في قاعدة البيانات</li>
                                    <li>متأكد 100% من عدم الحاجة للفرع</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-3">
                            <h6><i class="fas fa-lightbulb me-2"></i>نصيحة</h6>
                            <p class="mb-0">في معظم الحالات، يُنصح بـ <strong>إلغاء التفعيل</strong> بدلاً من الحذف النهائي. يمكنك دائماً حذف البيانات نهائياً لاحقاً إذا لزم الأمر.</p>
                        </div>
                    </div>
                </div>
            </main>
            
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        const branchCode = '<?php echo htmlspecialchars($branch['code']); ?>';
        const confirmInput = document.getElementById('confirm_code');
        const understandCheckbox = document.getElementById('understand_risk');
        const deleteButton = document.getElementById('deleteButton');
        
        function checkDeleteButton() {
            const codeMatches = confirmInput.value.trim() === branchCode;
            const riskUnderstood = understandCheckbox.checked;
            
            deleteButton.disabled = !(codeMatches && riskUnderstood);
            
            if (codeMatches) {
                confirmInput.classList.remove('is-invalid');
                confirmInput.classList.add('is-valid');
            } else {
                confirmInput.classList.remove('is-valid');
                if (confirmInput.value.trim() !== '') {
                    confirmInput.classList.add('is-invalid');
                }
            }
        }
        
        confirmInput.addEventListener('input', checkDeleteButton);
        understandCheckbox.addEventListener('change', checkDeleteButton);
        
        // تأكيد إضافي عند الإرسال
        document.getElementById('deleteForm').addEventListener('submit', function(e) {
            if (!confirm('هل أنت متأكد 100% من حذف هذا الفرع نهائياً؟\n\nهذا الإجراء لا يمكن التراجع عنه!')) {
                e.preventDefault();
            }
        });
        
        // تحويل النص إلى أحرف كبيرة
        confirmInput.addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });
    </script>
</body>
</html>
