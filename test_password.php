<?php
// اختبار تشفير كلمة المرور
$password = 'password';
$hash = '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm';

echo "<h3>اختبار كلمة المرور</h3>";
echo "<p>كلمة المرور: $password</p>";
echo "<p>الهاش: $hash</p>";

if (password_verify($password, $hash)) {
    echo "<p style='color: green;'>✓ كلمة المرور صحيحة!</p>";
} else {
    echo "<p style='color: red;'>✗ كلمة المرور خاطئة!</p>";
    
    // إنشاء هاش جديد
    $newHash = password_hash($password, PASSWORD_DEFAULT);
    echo "<p>هاش جديد: $newHash</p>";
}

// اختبار إنشاء هاش جديد
echo "<hr>";
echo "<h3>إنشاء هاش جديد</h3>";
$newHash = password_hash('password', PASSWORD_DEFAULT);
echo "<p>هاش جديد لكلمة 'password': $newHash</p>";

if (password_verify('password', $newHash)) {
    echo "<p style='color: green;'>✓ الهاش الجديد يعمل!</p>";
} else {
    echo "<p style='color: red;'>✗ الهاش الجديد لا يعمل!</p>";
}
?>
