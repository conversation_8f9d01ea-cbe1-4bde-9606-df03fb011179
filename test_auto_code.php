<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

checkSession();
$user = getUserById($_SESSION['user_id']);

// دالة توليد الكود التلقائي (نفس الدالة في add_branch.php)
function generateBranchCode($name) {
    // استخراج الأحرف الأولى من اسم الفرع
    $words = explode(' ', trim($name));
    $prefix = '';
    
    foreach ($words as $word) {
        if (!empty($word)) {
            // تحويل الأحرف العربية إلى إنجليزية
            $arabicToEnglish = [
                'ا' => 'A', 'أ' => 'A', 'إ' => 'A', 'آ' => 'A',
                'ب' => 'B', 'ت' => 'T', 'ث' => 'TH', 'ج' => 'J',
                'ح' => 'H', 'خ' => 'KH', 'د' => 'D', 'ذ' => 'DH',
                'ر' => 'R', 'ز' => 'Z', 'س' => 'S', 'ش' => 'SH',
                'ص' => 'S', 'ض' => 'D', 'ط' => 'T', 'ظ' => 'Z',
                'ع' => 'A', 'غ' => 'GH', 'ف' => 'F', 'ق' => 'Q',
                'ك' => 'K', 'ل' => 'L', 'م' => 'M', 'ن' => 'N',
                'ه' => 'H', 'و' => 'W', 'ي' => 'Y', 'ى' => 'Y',
                'ة' => 'H'
            ];
            
            $firstChar = mb_substr($word, 0, 1);
            if (isset($arabicToEnglish[$firstChar])) {
                $prefix .= $arabicToEnglish[$firstChar];
            } else {
                $prefix .= strtoupper($firstChar);
            }
        }
    }
    
    // التأكد من أن الكود لا يقل عن 3 أحرف
    if (strlen($prefix) < 3) {
        $prefix = strtoupper(substr(md5($name), 0, 3));
    }
    
    // قطع الكود إلى 3-4 أحرف كحد أقصى
    $prefix = substr($prefix, 0, 4);
    
    return $prefix . '001';
}

// أمثلة للاختبار
$testNames = [
    'فرع الزرقاء',
    'فرع عمان الرئيسي',
    'مركز إربد التجاري',
    'فرع العقبة الجنوبي',
    'مكتب الكرك',
    'فرع مادبا الشرقي',
    'مركز السلط الرئيسي',
    'فرع جرش الشمالي',
    'Main Branch',
    'Downtown Office',
    'North Center',
    'East Branch',
    'فرع',
    'مركز',
    'A',
    'AB',
    'فرع الزرقاء الجديد الكبير المتطور'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الكود التلقائي - نظام المحاسبة المالية</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body { 
            font-family: 'Cairo', sans-serif; 
            background-color: #f8f9fa;
        }
        
        /* تخطيط القائمة على اليمين */
        @media (min-width: 768px) {
            .main-content-with-right-sidebar {
                margin-right: 300px;
                padding-right: 20px;
                padding-left: 20px;
            }
        }
        
        @media (max-width: 767.98px) {
            .main-content-with-right-sidebar {
                margin-right: 0;
                padding-right: 15px;
                padding-left: 15px;
            }
        }
        
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .test-section {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .code-example {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        
        .code-example:hover {
            background: #e9ecef;
            border-color: #007bff;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,123,255,0.2);
        }
        
        .generated-code {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            font-size: 1.1em;
            padding: 8px 12px;
            border-radius: 5px;
            display: inline-block;
            margin-left: 10px;
        }
        
        .interactive-test {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border: 2px solid #2196f3;
            border-radius: 10px;
            padding: 20px;
        }
        
        .live-preview {
            background: #fff;
            border: 2px dashed #28a745;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            margin-top: 15px;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .preview-code {
            font-family: 'Courier New', monospace;
            font-size: 1.5em;
            font-weight: bold;
            color: #28a745;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: scale(0.8); }
            to { opacity: 1; transform: scale(1); }
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>
    
    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Main Content Area -->
            <main class="col-12 px-md-4 main-content-with-right-sidebar">
                <!-- رأس الصفحة -->
                <div class="test-header">
                    <h1 class="mb-3">
                        <i class="fas fa-magic me-3"></i>
                        اختبار نظام الكود التلقائي
                    </h1>
                    <p class="mb-0">نظام ذكي لتوليد أكواد فريدة للفروع تلقائياً من أسمائها</p>
                </div>

                <!-- الاختبار التفاعلي -->
                <div class="test-section">
                    <h3 class="mb-4">
                        <i class="fas fa-keyboard me-2 text-primary"></i>
                        اختبار تفاعلي
                    </h3>
                    
                    <div class="interactive-test">
                        <h5 class="text-primary mb-3">
                            <i class="fas fa-edit me-2"></i>
                            جرب بنفسك - اكتب اسم فرع وشاهد الكود المولد
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <label for="testInput" class="form-label">اسم الفرع:</label>
                                <input type="text" class="form-control form-control-lg" id="testInput" 
                                       placeholder="اكتب اسم الفرع هنا..." 
                                       value="فرع الزرقاء الرئيسي">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">الكود المولد:</label>
                                <div class="live-preview" id="livePreview">
                                    <span class="preview-code" id="previewCode">FZR001</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                الكود يتم توليده فوراً أثناء الكتابة
                            </small>
                        </div>
                    </div>
                </div>

                <!-- أمثلة محددة مسبقاً -->
                <div class="test-section">
                    <h3 class="mb-4">
                        <i class="fas fa-list me-2 text-success"></i>
                        أمثلة محددة مسبقاً
                    </h3>
                    
                    <div class="row">
                        <?php foreach ($testNames as $index => $name): ?>
                        <?php $generatedCode = generateBranchCode($name); ?>
                        <div class="col-lg-6 mb-3">
                            <div class="code-example">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">
                                            <i class="fas fa-store me-2 text-primary"></i>
                                            <?php echo htmlspecialchars($name); ?>
                                        </h6>
                                        <small class="text-muted">
                                            <?php 
                                            $words = explode(' ', trim($name));
                                            $explanation = [];
                                            foreach ($words as $word) {
                                                if (!empty($word)) {
                                                    $firstChar = mb_substr($word, 0, 1);
                                                    $explanation[] = $firstChar;
                                                }
                                            }
                                            echo 'الأحرف الأولى: ' . implode(' + ', $explanation);
                                            ?>
                                        </small>
                                    </div>
                                    <div>
                                        <span class="generated-code bg-<?php echo ['primary', 'success', 'info', 'warning', 'danger', 'secondary'][$index % 6]; ?> text-white">
                                            <?php echo $generatedCode; ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- شرح خوارزمية التوليد -->
                <div class="test-section">
                    <h3 class="mb-4">
                        <i class="fas fa-cogs me-2 text-info"></i>
                        كيف تعمل خوارزمية التوليد؟
                    </h3>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5>الخطوات:</h5>
                            <ol class="list-group list-group-numbered">
                                <li class="list-group-item">تقسيم اسم الفرع إلى كلمات منفصلة</li>
                                <li class="list-group-item">أخذ الحرف الأول من كل كلمة</li>
                                <li class="list-group-item">تحويل الأحرف العربية إلى إنجليزية</li>
                                <li class="list-group-item">التأكد من أن الكود لا يقل عن 3 أحرف</li>
                                <li class="list-group-item">قطع الكود إلى 4 أحرف كحد أقصى</li>
                                <li class="list-group-item">إضافة رقم تسلسلي (001, 002, ...)</li>
                                <li class="list-group-item">التحقق من عدم التكرار في قاعدة البيانات</li>
                            </ol>
                        </div>
                        
                        <div class="col-md-6">
                            <h5>خريطة تحويل الأحرف:</h5>
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>عربي</th>
                                            <th>إنجليزي</th>
                                            <th>عربي</th>
                                            <th>إنجليزي</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr><td>ا، أ، إ، آ</td><td>A</td><td>ف</td><td>F</td></tr>
                                        <tr><td>ب</td><td>B</td><td>ق</td><td>Q</td></tr>
                                        <tr><td>ت</td><td>T</td><td>ك</td><td>K</td></tr>
                                        <tr><td>ج</td><td>J</td><td>ل</td><td>L</td></tr>
                                        <tr><td>ح</td><td>H</td><td>م</td><td>M</td></tr>
                                        <tr><td>د</td><td>D</td><td>ن</td><td>N</td></tr>
                                        <tr><td>ر</td><td>R</td><td>ه</td><td>H</td></tr>
                                        <tr><td>ز</td><td>Z</td><td>و</td><td>W</td></tr>
                                        <tr><td>س</td><td>S</td><td>ي، ى</td><td>Y</td></tr>
                                        <tr><td>ع</td><td>A</td><td>ة</td><td>H</td></tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- مميزات النظام -->
                <div class="test-section">
                    <h3 class="mb-4">
                        <i class="fas fa-star me-2 text-warning"></i>
                        مميزات نظام الكود التلقائي
                    </h3>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card border-success h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-magic fa-3x text-success mb-3"></i>
                                    <h5>توليد تلقائي</h5>
                                    <p>لا حاجة لإدخال الكود يدوياً</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <div class="card border-primary h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-shield-alt fa-3x text-primary mb-3"></i>
                                    <h5>فريد ومحمي</h5>
                                    <p>لا يمكن تكرار الأكواد أو تعديلها</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <div class="card border-info h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-language fa-3x text-info mb-3"></i>
                                    <h5>دعم متعدد اللغات</h5>
                                    <p>يعمل مع العربية والإنجليزية</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار التنقل -->
                <div class="text-center">
                    <a href="add_branch.php" class="btn btn-primary btn-lg me-2">
                        <i class="fas fa-plus me-2"></i>
                        جرب إضافة فرع جديد
                    </a>
                    <a href="branches.php" class="btn btn-success btn-lg me-2">
                        <i class="fas fa-list me-2"></i>
                        قائمة الفروع
                    </a>
                    <a href="test_branch_management.php" class="btn btn-info btn-lg">
                        <i class="fas fa-test-tube me-2"></i>
                        اختبارات أخرى
                    </a>
                </div>
            </main>
            
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // دالة توليد معاينة الكود (نفس الدالة في add_branch.php)
        function generateCodePreview(name) {
            if (!name.trim()) {
                return '';
            }
            
            const words = name.trim().split(' ');
            let prefix = '';
            
            // خريطة تحويل الأحرف العربية
            const arabicToEnglish = {
                'ا': 'A', 'أ': 'A', 'إ': 'A', 'آ': 'A',
                'ب': 'B', 'ت': 'T', 'ث': 'TH', 'ج': 'J',
                'ح': 'H', 'خ': 'KH', 'د': 'D', 'ذ': 'DH',
                'ر': 'R', 'ز': 'Z', 'س': 'S', 'ش': 'SH',
                'ص': 'S', 'ض': 'D', 'ط': 'T', 'ظ': 'Z',
                'ع': 'A', 'غ': 'GH', 'ف': 'F', 'ق': 'Q',
                'ك': 'K', 'ل': 'L', 'م': 'M', 'ن': 'N',
                'ه': 'H', 'و': 'W', 'ي': 'Y', 'ى': 'Y',
                'ة': 'H'
            };
            
            words.forEach(word => {
                if (word) {
                    const firstChar = word.charAt(0);
                    if (arabicToEnglish[firstChar]) {
                        prefix += arabicToEnglish[firstChar];
                    } else {
                        prefix += firstChar.toUpperCase();
                    }
                }
            });
            
            // التأكد من أن الكود لا يقل عن 3 أحرف
            if (prefix.length < 3) {
                prefix = name.substring(0, 3).toUpperCase();
            }
            
            // قطع الكود إلى 4 أحرف كحد أقصى
            prefix = prefix.substring(0, 4);
            
            return prefix + '001';
        }
        
        // الاختبار التفاعلي
        document.getElementById('testInput').addEventListener('input', function() {
            const name = this.value.trim();
            const previewCode = document.getElementById('previewCode');
            const livePreview = document.getElementById('livePreview');
            
            if (name) {
                const generatedCode = generateCodePreview(name);
                previewCode.textContent = generatedCode;
                previewCode.className = 'preview-code fade-in';
                livePreview.style.borderColor = '#28a745';
                livePreview.style.backgroundColor = '#f8fff9';
            } else {
                previewCode.textContent = '---';
                previewCode.className = 'preview-code text-muted';
                livePreview.style.borderColor = '#dee2e6';
                livePreview.style.backgroundColor = '#fff';
            }
        });
        
        // تأثيرات بصرية عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير على أمثلة الأكواد
            document.querySelectorAll('.code-example').forEach((example, index) => {
                setTimeout(() => {
                    example.style.opacity = '0';
                    example.style.transform = 'translateY(20px)';
                    example.style.transition = 'all 0.5s ease';
                    
                    setTimeout(() => {
                        example.style.opacity = '1';
                        example.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
            
            // تشغيل الاختبار التفاعلي
            document.getElementById('testInput').dispatchEvent(new Event('input'));
        });
        
        // إضافة تأثيرات عند النقر على الأمثلة
        document.querySelectorAll('.code-example').forEach(example => {
            example.addEventListener('click', function() {
                const nameElement = this.querySelector('h6');
                const name = nameElement.textContent.replace(/^\s*[\s\S]*?\s*/, '').trim();
                
                document.getElementById('testInput').value = name;
                document.getElementById('testInput').dispatchEvent(new Event('input'));
                
                // تمرير إلى الاختبار التفاعلي
                document.querySelector('.interactive-test').scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
                
                // تأثير بصري
                this.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 200);
            });
        });
    </script>
</body>
</html>
