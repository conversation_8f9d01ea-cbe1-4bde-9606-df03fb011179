<?php
require_once 'config/database.php';

// إنشاء الجداول المطلوبة
try {
    // جدول الشركات
    $sql = "CREATE TABLE IF NOT EXISTS companies (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        code VARCHAR(50) UNIQUE NOT NULL,
        address TEXT,
        phone VARCHAR(20),
        email VARCHAR(100),
        tax_number VARCHAR(50),
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    executeQuery($sql);
    echo "✅ تم إنشاء جدول الشركات<br>";

    // جدول الفروع/المراكز
    $sql = "CREATE TABLE IF NOT EXISTS branches (
        id INT AUTO_INCREMENT PRIMARY KEY,
        company_id INT NULL,
        name VARCHAR(255) NOT NULL,
        code VARCHAR(50) UNIQUE NOT NULL,
        address TEXT,
        phone VARCHAR(20),
        manager_name VARCHAR(255),
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    executeQuery($sql);
    echo "✅ تم إنشاء جدول المراكز<br>";

    // جدول المستخدمين
    $sql = "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        full_name VARCHAR(255) NOT NULL,
        email VARCHAR(100),
        phone VARCHAR(20),
        user_type ENUM('admin', 'main_accountant', 'branch_accountant', 'cashier') NOT NULL,
        branch_id INT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    executeQuery($sql);
    echo "✅ تم إنشاء جدول المستخدمين<br>";

    // جدول نقاط البيع
    $sql = "CREATE TABLE IF NOT EXISTS point_of_sales (
        id INT AUTO_INCREMENT PRIMARY KEY,
        branch_id INT NOT NULL,
        name VARCHAR(255) NOT NULL,
        code VARCHAR(50) UNIQUE NOT NULL,
        type ENUM('supermarket', 'cafeteria', 'nuts_shop', 'sweets', 'bakery') NOT NULL,
        location VARCHAR(255),
        manager_name VARCHAR(255),
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    executeQuery($sql);
    echo "✅ تم إنشاء جدول نقاط البيع<br>";

    // جدول سجل الأنشطة
    $sql = "CREATE TABLE IF NOT EXISTS activity_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        action VARCHAR(255) NOT NULL,
        description TEXT,
        table_name VARCHAR(100),
        record_id INT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    executeQuery($sql);
    echo "✅ تم إنشاء جدول سجل الأنشطة<br>";

    // إنشاء مستخدم افتراضي إذا لم يكن موجود
    $adminExists = fetchOne("SELECT id FROM users WHERE username = 'admin'");

    if (!$adminExists) {
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $sql = "INSERT INTO users (username, password, full_name, user_type, is_active)
                VALUES ('admin', ?, 'مدير النظام', 'admin', TRUE)";
        executeQuery($sql, [$adminPassword]);
        echo "✅ تم إنشاء المستخدم الافتراضي (admin/admin123)<br>";
    }

    echo "<br><strong>🎉 تم إعداد قاعدة البيانات بنجاح!</strong><br>";
    echo "<a href='login.php' class='btn btn-primary'>الذهاب لصفحة تسجيل الدخول</a>";

} catch (Exception $e) {
    echo "❌ خطأ في إعداد قاعدة البيانات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد قاعدة البيانات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; padding: 50px; }
        .container { max-width: 800px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>إعداد قاعدة البيانات</h1>
        <div class="alert alert-info">
            <p>هذه الصفحة تقوم بإنشاء الجداول المطلوبة في قاعدة البيانات.</p>
        </div>
    </div>
</body>
</html>
