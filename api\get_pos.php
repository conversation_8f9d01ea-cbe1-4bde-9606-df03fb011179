<?php
header('Content-Type: application/json; charset=utf-8');
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit();
}

try {
    $branchId = $_GET['branch_id'] ?? '';
    
    if (empty($branchId)) {
        echo json_encode(['success' => false, 'message' => 'معرف الفرع مطلوب']);
        exit();
    }
    
    // التحقق من صلاحية الوصول للفرع
    $user = getUserById($_SESSION['user_id']);
    if ($user['user_type'] === 'branch_accountant' && $user['branch_id'] && $user['branch_id'] != $branchId) {
        echo json_encode(['success' => false, 'message' => 'غير مصرح لك بالوصول لهذا الفرع']);
        exit();
    }
    
    // جلب نقاط البيع للفرع المحدد
    $sql = "SELECT id, name, type, description 
            FROM point_of_sales 
            WHERE branch_id = ? AND is_active = 1 
            ORDER BY name";
    
    $pos = fetchAll($sql, [$branchId]);
    
    echo json_encode([
        'success' => true,
        'pos' => $pos
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
