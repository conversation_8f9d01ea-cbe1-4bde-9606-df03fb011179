<?php
header('Content-Type: application/json; charset=utf-8');
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit();
}

try {
    $user = getUserById($_SESSION['user_id']);
    $today = date('Y-m-d');
    $currentMonth = date('Y-m');
    
    // إحصائيات اليوم
    $todayStats = getTodayStatistics();
    
    // إحصائيات الشهر الحالي
    $monthSql = "SELECT 
                    COALESCE(SUM(CASE WHEN invoice_type = 'sale' THEN total_amount ELSE 0 END), 0) as monthly_sales,
                    COALESCE(SUM(CASE WHEN invoice_type = 'purchase' THEN total_amount ELSE 0 END), 0) as monthly_purchases,
                    COUNT(CASE WHEN invoice_type = 'sale' THEN 1 END) as monthly_sales_count,
                    COUNT(CASE WHEN invoice_type = 'purchase' THEN 1 END) as monthly_purchases_count
                 FROM invoices 
                 WHERE DATE_FORMAT(invoice_date, '%Y-%m') = ? 
                 AND status != 'cancelled'";
    
    $params = [$currentMonth];
    
    // فلتر الفرع للمحاسب الفرعي
    if ($user['user_type'] === 'branch_accountant' && $user['branch_id']) {
        $monthSql .= " AND branch_id = ?";
        $params[] = $user['branch_id'];
    }
    
    $monthStats = fetchOne($monthSql, $params);
    
    // إحصائيات المخزون
    $inventorySql = "SELECT 
                        COUNT(*) as total_products,
                        COUNT(CASE WHEN i.current_stock <= p.min_stock_level THEN 1 END) as low_stock_products,
                        COALESCE(SUM(i.current_stock * p.cost_price), 0) as inventory_value
                     FROM inventory i
                     JOIN products p ON i.product_id = p.id
                     WHERE p.is_active = 1";
    
    $inventoryParams = [];
    if ($user['user_type'] === 'branch_accountant' && $user['branch_id']) {
        $inventorySql .= " AND i.branch_id = ?";
        $inventoryParams[] = $user['branch_id'];
    }
    
    $inventoryStats = fetchOne($inventorySql, $inventoryParams);
    
    // إحصائيات العملاء والموردين
    $customersSql = "SELECT 
                        COUNT(*) as total_customers,
                        COALESCE(SUM(current_balance), 0) as total_receivables
                     FROM customers 
                     WHERE is_active = 1";
    
    $customersStats = fetchOne($customersSql);
    
    $suppliersSql = "SELECT 
                        COUNT(*) as total_suppliers,
                        COALESCE(SUM(current_balance), 0) as total_payables
                     FROM suppliers 
                     WHERE is_active = 1";
    
    $suppliersStats = fetchOne($suppliersSql);
    
    // بيانات الرسوم البيانية
    $salesChartData = getMonthlySalesData();
    $branchChartData = getBranchPerformanceData();
    
    // أفضل المنتجات مبيعاً
    $topProductsSql = "SELECT p.name, SUM(ii.quantity) as total_sold, SUM(ii.total_amount) as total_revenue
                       FROM invoice_items ii
                       JOIN invoices i ON ii.invoice_id = i.id
                       JOIN products p ON ii.product_id = p.id
                       WHERE i.invoice_type = 'sale' 
                       AND i.status != 'cancelled'
                       AND DATE_FORMAT(i.invoice_date, '%Y-%m') = ?";
    
    $topProductsParams = [$currentMonth];
    
    if ($user['user_type'] === 'branch_accountant' && $user['branch_id']) {
        $topProductsSql .= " AND i.branch_id = ?";
        $topProductsParams[] = $user['branch_id'];
    }
    
    $topProductsSql .= " GROUP BY p.id, p.name ORDER BY total_revenue DESC LIMIT 5";
    $topProducts = fetchAll($topProductsSql, $topProductsParams);
    
    // أحدث الفواتير
    $recentInvoicesSql = "SELECT i.invoice_number, i.invoice_type, i.total_amount, i.invoice_date,
                                 c.name as customer_name, s.name as supplier_name, b.name as branch_name
                          FROM invoices i
                          LEFT JOIN customers c ON i.customer_id = c.id
                          LEFT JOIN suppliers s ON i.supplier_id = s.id
                          JOIN branches b ON i.branch_id = b.id
                          WHERE i.status != 'cancelled'";
    
    $recentInvoicesParams = [];
    
    if ($user['user_type'] === 'branch_accountant' && $user['branch_id']) {
        $recentInvoicesSql .= " AND i.branch_id = ?";
        $recentInvoicesParams[] = $user['branch_id'];
    }
    
    $recentInvoicesSql .= " ORDER BY i.created_at DESC LIMIT 10";
    $recentInvoices = fetchAll($recentInvoicesSql, $recentInvoicesParams);
    
    // تجميع البيانات
    $response = [
        'success' => true,
        'statistics' => [
            'today' => $todayStats,
            'month' => [
                'sales' => (float)$monthStats['monthly_sales'],
                'purchases' => (float)$monthStats['monthly_purchases'],
                'sales_count' => (int)$monthStats['monthly_sales_count'],
                'purchases_count' => (int)$monthStats['monthly_purchases_count'],
                'profit' => (float)$monthStats['monthly_sales'] - (float)$monthStats['monthly_purchases']
            ],
            'inventory' => [
                'total_products' => (int)$inventoryStats['total_products'],
                'low_stock_products' => (int)$inventoryStats['low_stock_products'],
                'inventory_value' => (float)$inventoryStats['inventory_value']
            ],
            'customers' => [
                'total_customers' => (int)$customersStats['total_customers'],
                'total_receivables' => (float)$customersStats['total_receivables']
            ],
            'suppliers' => [
                'total_suppliers' => (int)$suppliersStats['total_suppliers'],
                'total_payables' => (float)$suppliersStats['total_payables']
            ]
        ],
        'charts' => [
            'sales' => $salesChartData,
            'branches' => $branchChartData
        ],
        'top_products' => $topProducts,
        'recent_invoices' => $recentInvoices,
        'timestamp' => time()
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في جلب الإحصائيات: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
