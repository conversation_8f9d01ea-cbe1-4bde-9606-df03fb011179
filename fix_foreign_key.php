<?php
require_once 'config/database.php';

echo "<h2>إصلاح مشكلة القيد المرجعي</h2>";

try {
    echo "<p>🔄 جاري إصلاح جدول branches...</p>";
    
    // إزالة القيد المرجعي الموجود
    try {
        executeQuery("ALTER TABLE branches DROP FOREIGN KEY branches_ibfk_1");
        echo "<p style='color: green;'>✅ تم إزالة القيد المرجعي القديم</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ القيد المرجعي غير موجود أو تم حذفه مسبقاً</p>";
    }
    
    // تعديل العمود ليقبل NULL
    executeQuery("ALTER TABLE branches MODIFY COLUMN company_id INT NULL");
    echo "<p style='color: green;'>✅ تم تعديل عمود company_id ليقبل NULL</p>";
    
    // إضافة القيد المرجعي الجديد مع ON DELETE SET NULL
    try {
        executeQuery("ALTER TABLE branches ADD CONSTRAINT fk_branches_company 
                     FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE SET NULL");
        echo "<p style='color: green;'>✅ تم إضافة القيد المرجعي الجديد مع SET NULL</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ لم يتم إضافة القيد المرجعي (قد يكون غير ضروري)</p>";
    }
    
    // اختبار إدراج بيانات تجريبية
    echo "<p>🧪 اختبار إدراج مركز تجريبي...</p>";
    
    $testData = [
        'name' => 'مركز اختبار إصلاح القيد',
        'code' => 'TEST001',
        'address' => 'عنوان تجريبي',
        'phone' => '*********',
        'manager_name' => 'مدير تجريبي'
    ];
    
    $sql = "INSERT INTO branches (name, code, address, phone, manager_name, company_id, created_at) 
            VALUES (?, ?, ?, ?, ?, NULL, NOW())";
    
    $stmt = executeQuery($sql, [
        $testData['name'],
        $testData['code'],
        $testData['address'],
        $testData['phone'],
        $testData['manager_name']
    ]);
    
    $insertId = getLastInsertId();
    echo "<p style='color: green;'>✅ تم إدراج المركز التجريبي بنجاح! ID: $insertId</p>";
    
    // حذف البيانات التجريبية
    executeQuery("DELETE FROM branches WHERE id = ?", [$insertId]);
    echo "<p style='color: blue;'>🗑️ تم حذف البيانات التجريبية</p>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 20px 0; border: 1px solid #c3e6cb;'>";
    echo "<h3 style='color: #155724;'>🎉 تم إصلاح المشكلة بنجاح!</h3>";
    echo "<p>الآن يمكنك إضافة المراكز بدون أخطاء.</p>";
    echo "</div>";
    
    // عرض هيكل الجدول المحدث
    echo "<h3>هيكل جدول branches المحدث:</h3>";
    $columns = fetchAll("DESCRIBE branches");
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f2f2f2;'>";
    echo "<th>اسم العمود</th><th>النوع</th><th>NULL</th><th>المفتاح</th><th>القيمة الافتراضية</th>";
    echo "</tr>";
    
    foreach ($columns as $column) {
        $nullColor = $column['Null'] == 'YES' ? 'green' : 'red';
        echo "<tr>";
        echo "<td><strong>{$column['Field']}</strong></td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td style='color: $nullColor;'>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</p>";
    
    // محاولة حل بديل - إعادة إنشاء الجدول
    echo "<h3>محاولة حل بديل:</h3>";
    echo "<p>سيتم إعادة إنشاء جدول branches بدون قيود مرجعية...</p>";
    
    try {
        // نسخ البيانات الموجودة
        $existingData = fetchAll("SELECT * FROM branches");
        
        // حذف الجدول القديم
        executeQuery("DROP TABLE IF EXISTS branches");
        
        // إنشاء الجدول الجديد بدون قيود مرجعية
        $sql = "CREATE TABLE branches (
            id INT AUTO_INCREMENT PRIMARY KEY,
            company_id INT NULL,
            name VARCHAR(255) NOT NULL,
            code VARCHAR(50) UNIQUE NOT NULL,
            address TEXT,
            phone VARCHAR(20),
            manager_name VARCHAR(255),
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        executeQuery($sql);
        
        // استعادة البيانات
        if (!empty($existingData)) {
            foreach ($existingData as $row) {
                $sql = "INSERT INTO branches (id, company_id, name, code, address, phone, manager_name, is_active, created_at, updated_at) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                executeQuery($sql, [
                    $row['id'], $row['company_id'], $row['name'], $row['code'],
                    $row['address'], $row['phone'], $row['manager_name'],
                    $row['is_active'], $row['created_at'], $row['updated_at']
                ]);
            }
        }
        
        echo "<p style='color: green;'>✅ تم إعادة إنشاء الجدول بنجاح!</p>";
        
    } catch (Exception $e2) {
        echo "<p style='color: red;'>❌ فشل في الحل البديل: " . htmlspecialchars($e2->getMessage()) . "</p>";
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشكلة القيد المرجعي</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
            max-width: 900px; 
            margin: 0 auto; 
        }
        table { 
            width: 100%; 
            margin: 10px 0; 
        }
        th, td { 
            padding: 8px; 
            text-align: right; 
            border: 1px solid #ddd;
        }
        th { 
            background: #f8f9fa; 
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            display: inline-block;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <div style="text-align: center; margin-top: 20px;">
            <a href="add_branch.php" class="btn btn-success">جرب إضافة مركز الآن</a>
            <a href="branches.php" class="btn">قائمة المراكز</a>
            <a href="index.php" class="btn">الصفحة الرئيسية</a>
        </div>
    </div>
</body>
</html>
