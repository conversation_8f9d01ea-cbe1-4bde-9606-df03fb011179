<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

checkSession();

$invoiceId = $_GET['id'] ?? '';
$success = $_GET['success'] ?? '';

if (empty($invoiceId)) {
    header('Location: sales.php');
    exit();
}

// جلب بيانات الفاتورة
$sql = "SELECT i.*, c.name as customer_name, c.phone as customer_phone, c.address as customer_address,
               s.name as supplier_name, s.phone as supplier_phone, s.address as supplier_address,
               b.name as branch_name, b.address as branch_address, b.phone as branch_phone,
               p.name as pos_name, u.full_name as user_name
        FROM invoices i
        LEFT JOIN customers c ON i.customer_id = c.id
        LEFT JOIN suppliers s ON i.supplier_id = s.id
        JOIN branches b ON i.branch_id = b.id
        LEFT JOIN point_of_sales p ON i.pos_id = p.id
        JOIN users u ON i.user_id = u.id
        WHERE i.id = ?";

$invoice = fetchOne($sql, [$invoiceId]);

if (!$invoice) {
    header('Location: sales.php?error=invoice_not_found');
    exit();
}

// التحقق من صلاحية الوصول
$user = getUserById($_SESSION['user_id']);
if ($user['user_type'] === 'branch_accountant' && $user['branch_id'] && $user['branch_id'] != $invoice['branch_id']) {
    header('Location: sales.php?error=access_denied');
    exit();
}

// جلب تفاصيل الفاتورة
$itemsSql = "SELECT ii.*, p.name as product_name, p.unit
             FROM invoice_items ii
             JOIN products p ON ii.product_id = p.id
             WHERE ii.invoice_id = ?
             ORDER BY ii.id";

$items = fetchAll($itemsSql, [$invoiceId]);

// جلب المدفوعات
$paymentsSql = "SELECT p.*, u.full_name as user_name
                FROM payments p
                JOIN users u ON p.user_id = u.id
                WHERE p.invoice_id = ?
                ORDER BY p.payment_date DESC, p.created_at DESC";

$payments = fetchAll($paymentsSql, [$invoiceId]);

// جلب معلومات الشركة
$company = fetchOne("SELECT * FROM companies LIMIT 1");
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض الفاتورة <?php echo htmlspecialchars($invoice['invoice_number']); ?> - نظام المحاسبة المالية</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        @media print {
            .no-print { display: none !important; }
            .container-fluid { margin: 0; padding: 0; }
            .card { border: 1px solid #000 !important; box-shadow: none !important; }
            body { font-size: 12px; }
        }
        
        .invoice-header {
            border-bottom: 3px solid #007bff;
            margin-bottom: 30px;
            padding-bottom: 20px;
        }
        
        .invoice-details {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .status-badge {
            font-size: 1.1em;
            padding: 8px 16px;
        }
        
        .invoice-table th {
            background-color: #007bff;
            color: white;
            font-weight: 600;
        }
        
        .invoice-summary {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
        }
        
        .payment-history {
            background-color: #e9ecef;
            border-radius: 8px;
            padding: 15px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <div class="no-print">
        <?php include '../../includes/navbar.php'; ?>
    </div>
    
    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="no-print">
                <?php include '../../includes/sidebar.php'; ?>
            </div>
            
            <!-- Main Content Area -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom no-print">
                    <h1 class="h2">
                        <i class="fas fa-file-invoice me-2"></i>
                        عرض الفاتورة
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="<?php echo $invoice['invoice_type'] === 'sale' ? 'sales.php' : 'purchases.php'; ?>" 
                               class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right"></i> العودة للقائمة
                            </a>
                            <?php if ($invoice['status'] !== 'cancelled'): ?>
                            <a href="edit_invoice.php?id=<?php echo $invoice['id']; ?>" 
                               class="btn btn-outline-primary">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            <?php endif; ?>
                            <button onclick="window.print()" class="btn btn-outline-info">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                            <a href="print_invoice.php?id=<?php echo $invoice['id']; ?>" 
                               class="btn btn-outline-success" target="_blank">
                                <i class="fas fa-file-pdf"></i> PDF
                            </a>
                        </div>
                    </div>
                </div>

                <?php if ($success): ?>
                <div class="alert alert-success no-print">
                    <i class="fas fa-check-circle me-2"></i>
                    تم إنشاء الفاتورة بنجاح!
                </div>
                <?php endif; ?>

                <!-- محتوى الفاتورة -->
                <div class="card">
                    <div class="card-body">
                        <!-- رأس الفاتورة -->
                        <div class="invoice-header">
                            <div class="row">
                                <div class="col-md-6">
                                    <h2 class="text-primary mb-3">
                                        <?php echo $company ? htmlspecialchars($company['name']) : 'اسم الشركة'; ?>
                                    </h2>
                                    <?php if ($company): ?>
                                    <p class="mb-1">
                                        <i class="fas fa-map-marker-alt me-2"></i>
                                        <?php echo htmlspecialchars($company['address']); ?>
                                    </p>
                                    <p class="mb-1">
                                        <i class="fas fa-phone me-2"></i>
                                        <?php echo htmlspecialchars($company['phone']); ?>
                                    </p>
                                    <p class="mb-1">
                                        <i class="fas fa-envelope me-2"></i>
                                        <?php echo htmlspecialchars($company['email']); ?>
                                    </p>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-6 text-end">
                                    <h3 class="text-primary mb-3">
                                        <?php echo $invoice['invoice_type'] === 'sale' ? 'فاتورة مبيعات' : 'فاتورة مشتريات'; ?>
                                    </h3>
                                    <h4 class="mb-2">رقم الفاتورة: <?php echo htmlspecialchars($invoice['invoice_number']); ?></h4>
                                    <p class="mb-1">تاريخ الفاتورة: <?php echo date('Y-m-d', strtotime($invoice['invoice_date'])); ?></p>
                                    <?php if ($invoice['due_date']): ?>
                                    <p class="mb-1">تاريخ الاستحقاق: <?php echo date('Y-m-d', strtotime($invoice['due_date'])); ?></p>
                                    <?php endif; ?>
                                    <p class="mb-3">
                                        الحالة: 
                                        <?php
                                        $statusClass = '';
                                        $statusText = '';
                                        switch ($invoice['status']) {
                                            case 'draft':
                                                $statusClass = 'bg-secondary';
                                                $statusText = 'مسودة';
                                                break;
                                            case 'pending':
                                                $statusClass = 'bg-warning';
                                                $statusText = 'في الانتظار';
                                                break;
                                            case 'approved':
                                                $statusClass = 'bg-info';
                                                $statusText = 'معتمدة';
                                                break;
                                            case 'paid':
                                                $statusClass = 'bg-success';
                                                $statusText = 'مدفوعة';
                                                break;
                                            case 'cancelled':
                                                $statusClass = 'bg-danger';
                                                $statusText = 'ملغية';
                                                break;
                                        }
                                        ?>
                                        <span class="badge status-badge <?php echo $statusClass; ?>">
                                            <?php echo $statusText; ?>
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- تفاصيل العميل/المورد والفرع -->
                        <div class="invoice-details">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5 class="mb-3">
                                        <i class="fas fa-<?php echo $invoice['invoice_type'] === 'sale' ? 'user' : 'truck'; ?> me-2"></i>
                                        <?php echo $invoice['invoice_type'] === 'sale' ? 'بيانات العميل' : 'بيانات المورد'; ?>
                                    </h5>
                                    <?php if ($invoice['invoice_type'] === 'sale'): ?>
                                        <?php if ($invoice['customer_name']): ?>
                                        <p class="mb-1"><strong>الاسم:</strong> <?php echo htmlspecialchars($invoice['customer_name']); ?></p>
                                        <?php if ($invoice['customer_phone']): ?>
                                        <p class="mb-1"><strong>الهاتف:</strong> <?php echo htmlspecialchars($invoice['customer_phone']); ?></p>
                                        <?php endif; ?>
                                        <?php if ($invoice['customer_address']): ?>
                                        <p class="mb-1"><strong>العنوان:</strong> <?php echo htmlspecialchars($invoice['customer_address']); ?></p>
                                        <?php endif; ?>
                                        <?php else: ?>
                                        <p class="text-muted">عميل نقدي</p>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <p class="mb-1"><strong>الاسم:</strong> <?php echo htmlspecialchars($invoice['supplier_name']); ?></p>
                                        <?php if ($invoice['supplier_phone']): ?>
                                        <p class="mb-1"><strong>الهاتف:</strong> <?php echo htmlspecialchars($invoice['supplier_phone']); ?></p>
                                        <?php endif; ?>
                                        <?php if ($invoice['supplier_address']): ?>
                                        <p class="mb-1"><strong>العنوان:</strong> <?php echo htmlspecialchars($invoice['supplier_address']); ?></p>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-6">
                                    <h5 class="mb-3">
                                        <i class="fas fa-store me-2"></i>
                                        بيانات الفرع
                                    </h5>
                                    <p class="mb-1"><strong>الفرع:</strong> <?php echo htmlspecialchars($invoice['branch_name']); ?></p>
                                    <?php if ($invoice['pos_name']): ?>
                                    <p class="mb-1"><strong>نقطة البيع:</strong> <?php echo htmlspecialchars($invoice['pos_name']); ?></p>
                                    <?php endif; ?>
                                    <p class="mb-1"><strong>المستخدم:</strong> <?php echo htmlspecialchars($invoice['user_name']); ?></p>
                                    <p class="mb-1"><strong>تاريخ الإنشاء:</strong> <?php echo date('Y-m-d H:i', strtotime($invoice['created_at'])); ?></p>
                                </div>
                            </div>
                        </div>

                        <!-- جدول الأصناف -->
                        <div class="table-responsive mb-4">
                            <table class="table table-bordered invoice-table">
                                <thead>
                                    <tr>
                                        <th width="5%">#</th>
                                        <th width="35%">المنتج</th>
                                        <th width="10%">الوحدة</th>
                                        <th width="10%">الكمية</th>
                                        <th width="12%">سعر الوحدة</th>
                                        <th width="8%">خصم %</th>
                                        <th width="8%">ضريبة %</th>
                                        <th width="12%">الإجمالي</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($items as $index => $item): ?>
                                    <tr>
                                        <td><?php echo $index + 1; ?></td>
                                        <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                                        <td><?php echo htmlspecialchars($item['unit']); ?></td>
                                        <td><?php echo number_format($item['quantity'], 2); ?></td>
                                        <td><?php echo number_format($item['unit_price'], 2); ?> د.أ</td>
                                        <td><?php echo number_format($item['discount_percentage'], 1); ?>%</td>
                                        <td><?php echo number_format($item['tax_percentage'], 1); ?>%</td>
                                        <td><?php echo number_format($item['total_amount'], 2); ?> د.أ</td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- ملخص الفاتورة -->
                        <div class="row">
                            <div class="col-md-8">
                                <?php if ($invoice['notes']): ?>
                                <div class="mb-4">
                                    <h5><i class="fas fa-sticky-note me-2"></i>ملاحظات</h5>
                                    <p class="text-muted"><?php echo nl2br(htmlspecialchars($invoice['notes'])); ?></p>
                                </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($payments)): ?>
                                <div class="payment-history">
                                    <h5 class="mb-3"><i class="fas fa-money-bill me-2"></i>سجل المدفوعات</h5>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>التاريخ</th>
                                                    <th>المبلغ</th>
                                                    <th>الطريقة</th>
                                                    <th>رقم المرجع</th>
                                                    <th>المستخدم</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($payments as $payment): ?>
                                                <tr>
                                                    <td><?php echo date('Y-m-d', strtotime($payment['payment_date'])); ?></td>
                                                    <td><?php echo number_format($payment['amount'], 2); ?> د.أ</td>
                                                    <td>
                                                        <?php
                                                        $methods = [
                                                            'cash' => 'نقدي',
                                                            'bank_transfer' => 'تحويل بنكي',
                                                            'check' => 'شيك',
                                                            'credit_card' => 'بطاقة ائتمان'
                                                        ];
                                                        echo $methods[$payment['payment_method']] ?? $payment['payment_method'];
                                                        ?>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($payment['reference_number'] ?? '-'); ?></td>
                                                    <td><?php echo htmlspecialchars($payment['user_name']); ?></td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="invoice-summary">
                                    <h5 class="mb-3"><i class="fas fa-calculator me-2"></i>ملخص الفاتورة</h5>
                                    
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>المجموع الفرعي:</span>
                                        <span><?php echo number_format($invoice['subtotal'], 2); ?> د.أ</span>
                                    </div>
                                    
                                    <?php if ($invoice['discount_amount'] > 0): ?>
                                    <div class="d-flex justify-content-between mb-2 text-danger">
                                        <span>إجمالي الخصم:</span>
                                        <span>-<?php echo number_format($invoice['discount_amount'], 2); ?> د.أ</span>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($invoice['tax_amount'] > 0): ?>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>إجمالي الضريبة:</span>
                                        <span><?php echo number_format($invoice['tax_amount'], 2); ?> د.أ</span>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <hr>
                                    
                                    <div class="d-flex justify-content-between mb-2">
                                        <strong>الإجمالي النهائي:</strong>
                                        <strong><?php echo number_format($invoice['total_amount'], 2); ?> د.أ</strong>
                                    </div>
                                    
                                    <div class="d-flex justify-content-between mb-2 text-success">
                                        <span>المبلغ المدفوع:</span>
                                        <span><?php echo number_format($invoice['paid_amount'], 2); ?> د.أ</span>
                                    </div>
                                    
                                    <?php if ($invoice['remaining_amount'] > 0): ?>
                                    <div class="d-flex justify-content-between text-warning">
                                        <strong>المبلغ المتبقي:</strong>
                                        <strong><?php echo number_format($invoice['remaining_amount'], 2); ?> د.أ</strong>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                
                                <?php if ($invoice['remaining_amount'] > 0 && $invoice['status'] !== 'cancelled'): ?>
                                <div class="mt-3 no-print">
                                    <button type="button" class="btn btn-success w-100" onclick="addPayment()">
                                        <i class="fas fa-money-bill me-2"></i>
                                        إضافة دفعة
                                    </button>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- نافذة إضافة دفعة -->
    <div class="modal fade" id="paymentModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة دفعة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="paymentForm">
                    <div class="modal-body">
                        <input type="hidden" name="invoice_id" value="<?php echo $invoice['id']; ?>">
                        
                        <div class="mb-3">
                            <label for="payment_amount" class="form-label">مبلغ الدفعة</label>
                            <input type="number" class="form-control" id="payment_amount" name="payment_amount" 
                                   step="0.01" max="<?php echo $invoice['remaining_amount']; ?>" required>
                            <div class="form-text">الحد الأقصى: <?php echo number_format($invoice['remaining_amount'], 2); ?> د.أ</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="payment_method" class="form-label">طريقة الدفع</label>
                            <select class="form-select" id="payment_method" name="payment_method" required>
                                <option value="cash">نقدي</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                                <option value="check">شيك</option>
                                <option value="credit_card">بطاقة ائتمان</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="payment_date" class="form-label">تاريخ الدفع</label>
                            <input type="date" class="form-control" id="payment_date" name="payment_date" 
                                   value="<?php echo date('Y-m-d'); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="reference_number" class="form-label">رقم المرجع</label>
                            <input type="text" class="form-control" id="reference_number" name="reference_number">
                        </div>
                        
                        <div class="mb-3">
                            <label for="payment_notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="payment_notes" name="payment_notes" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ الدفعة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function addPayment() {
            const modal = new bootstrap.Modal(document.getElementById('paymentModal'));
            modal.show();
        }

        // معالجة نموذج الدفعة
        document.getElementById('paymentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch('../../api/add_payment.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم إضافة الدفعة بنجاح');
                    location.reload();
                } else {
                    alert('حدث خطأ: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال');
            });
        });
    </script>
</body>
</html>
