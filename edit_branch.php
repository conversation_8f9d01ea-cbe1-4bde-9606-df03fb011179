<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

checkSession();

$user = getUserById($_SESSION['user_id']);

// التحقق من الصلاحيات
if (!isAdmin($_SESSION['user_id'])) {
    header('Location: index.php?error=access_denied');
    exit();
}

$error = '';
$success = '';
$branchId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$branchId) {
    header('Location: branches.php?error=invalid_id');
    exit();
}

// جلب بيانات الفرع
try {
    $branch = fetchOne("SELECT * FROM branches WHERE id = ?", [$branchId]);
    if (!$branch) {
        header('Location: branches.php?error=branch_not_found');
        exit();
    }
} catch (Exception $e) {
    header('Location: branches.php?error=database_error');
    exit();
}

// جلب الشركات
try {
    $companies = fetchAll("SELECT * FROM companies WHERE is_active = 1 ORDER BY name");
} catch (Exception $e) {
    $companies = [];
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $companyId = !empty($_POST['company_id']) ? $_POST['company_id'] : null;
        $name = trim($_POST['name']);
        $address = trim($_POST['address']);
        $phone = trim($_POST['phone']);
        $managerName = trim($_POST['manager_name']);
        $isActive = isset($_POST['is_active']) ? 1 : 0;

        // التحقق من صحة البيانات
        if (empty($name)) {
            throw new Exception('يرجى إدخال اسم الفرع');
        }

        // تحديث بيانات الفرع
        $sql = "UPDATE branches SET
                company_id = ?,
                name = ?,
                address = ?,
                phone = ?,
                manager_name = ?,
                is_active = ?,
                updated_at = NOW()
                WHERE id = ?";

        executeQuery($sql, [$companyId, $name, $address, $phone, $managerName, $isActive, $branchId]);

        // تسجيل النشاط
        logActivity($_SESSION['user_id'], 'تعديل فرع', "تم تعديل الفرع: $name ({$branch['code']})");

        $success = 'تم تحديث بيانات الفرع بنجاح';

        // تحديث البيانات المحلية
        $branch['company_id'] = $companyId;
        $branch['name'] = $name;
        $branch['address'] = $address;
        $branch['phone'] = $phone;
        $branch['manager_name'] = $managerName;
        $branch['is_active'] = $isActive;

    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// جلب نقاط البيع التابعة للفرع
try {
    $pointOfSales = fetchAll("SELECT * FROM point_of_sales WHERE branch_id = ? ORDER BY name", [$branchId]);
} catch (Exception $e) {
    $pointOfSales = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل الفرع - نظام المحاسبة المالية</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">

    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body { font-family: 'Cairo', sans-serif; }

        /* تخطيط القائمة على اليمين */
        @media (min-width: 768px) {
            .main-content-with-right-sidebar {
                margin-right: 300px;
                padding-right: 20px;
                padding-left: 20px;
            }
        }

        @media (max-width: 767.98px) {
            .main-content-with-right-sidebar {
                margin-right: 0;
                padding-right: 15px;
                padding-left: 15px;
            }
        }

        .form-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .form-header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 20px;
            margin: -20px -20px 20px -20px;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .required {
            color: #dc3545;
        }

        .help-text {
            font-size: 0.875rem;
            color: #6c757d;
        }

        .readonly-field {
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .pos-item {
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Main Content Area -->
            <main class="col-12 px-md-4 main-content-with-right-sidebar">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-edit me-2"></i>
                        تعديل الفرع: <?php echo htmlspecialchars($branch['name']); ?>
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="branches.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right"></i> العودة للقائمة
                            </a>
                            <a href="view_branch.php?id=<?php echo $branchId; ?>" class="btn btn-outline-info">
                                <i class="fas fa-eye"></i> عرض التفاصيل
                            </a>
                        </div>
                    </div>
                </div>

                <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <div class="row">
                    <!-- نموذج تعديل الفرع -->
                    <div class="col-lg-8">
                        <div class="form-container p-4">
                            <div class="form-header">
                                <h4 class="mb-0">
                                    <i class="fas fa-edit me-2"></i>
                                    تعديل بيانات الفرع
                                </h4>
                            </div>

                            <form method="POST" id="branchForm" novalidate>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="company_id" class="form-label">الشركة</label>
                                            <select class="form-select" id="company_id" name="company_id">
                                                <option value="">اختر الشركة (اختياري)</option>
                                                <?php foreach ($companies as $company): ?>
                                                <option value="<?php echo $company['id']; ?>"
                                                        <?php echo ($branch['company_id'] == $company['id']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($company['name']); ?>
                                                </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="code" class="form-label">كود الفرع</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-warning text-dark">
                                                    <i class="fas fa-lock"></i>
                                                </span>
                                                <input type="text" class="form-control readonly-field" id="code"
                                                       value="<?php echo htmlspecialchars($branch['code']); ?>" readonly>
                                            </div>
                                            <div class="help-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                هذا الكود تم توليده تلقائياً ولا يمكن تعديله لضمان الأمان
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="name" class="form-label">اسم الفرع <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name"
                                           value="<?php echo htmlspecialchars($branch['name']); ?>"
                                           placeholder="مثال: فرع الزرقاء" required maxlength="100">
                                    <div class="invalid-feedback">
                                        يرجى إدخال اسم الفرع
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="address" class="form-label">العنوان</label>
                                    <textarea class="form-control" id="address" name="address" rows="3"
                                              placeholder="العنوان التفصيلي للفرع"><?php echo htmlspecialchars($branch['address']); ?></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="phone" class="form-label">رقم الهاتف</label>
                                            <input type="tel" class="form-control" id="phone" name="phone"
                                                   value="<?php echo htmlspecialchars($branch['phone']); ?>"
                                                   placeholder="+962-6-1234567" maxlength="20">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="manager_name" class="form-label">اسم المدير</label>
                                            <input type="text" class="form-control" id="manager_name" name="manager_name"
                                                   value="<?php echo htmlspecialchars($branch['manager_name']); ?>"
                                                   placeholder="اسم مدير الفرع" maxlength="100">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                               <?php echo $branch['is_active'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="is_active">
                                            الفرع نشط
                                        </label>
                                    </div>
                                    <div class="help-text">إلغاء تفعيل الفرع سيمنع استخدامه في العمليات الجديدة</div>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                                    <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                        <i class="fas fa-undo me-2"></i>
                                        إلغاء التغييرات
                                    </button>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ التغييرات
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="col-lg-4">
                        <!-- معلومات الفرع -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    معلومات الفرع
                                </h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>تاريخ الإنشاء:</strong></td>
                                        <td><?php echo date('Y-m-d', strtotime($branch['created_at'])); ?></td>
                                    </tr>
                                    <?php if ($branch['updated_at']): ?>
                                    <tr>
                                        <td><strong>آخر تحديث:</strong></td>
                                        <td><?php echo date('Y-m-d H:i', strtotime($branch['updated_at'])); ?></td>
                                    </tr>
                                    <?php endif; ?>
                                    <tr>
                                        <td><strong>الحالة:</strong></td>
                                        <td>
                                            <?php if ($branch['is_active']): ?>
                                            <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                            <span class="badge bg-danger">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- نقاط البيع -->
                        <div class="card mt-3">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-cash-register me-2"></i>
                                    نقاط البيع (<?php echo count($pointOfSales); ?>)
                                </h5>
                                <a href="add_pos.php?branch_id=<?php echo $branchId; ?>" class="btn btn-sm btn-primary">
                                    <i class="fas fa-plus"></i> إضافة
                                </a>
                            </div>
                            <div class="card-body">
                                <?php if (empty($pointOfSales)): ?>
                                <div class="text-center text-muted">
                                    <i class="fas fa-cash-register fa-3x mb-3"></i>
                                    <p>لا توجد نقاط بيع مضافة بعد</p>
                                    <a href="add_pos.php?branch_id=<?php echo $branchId; ?>" class="btn btn-sm btn-outline-primary">
                                        إضافة نقطة بيع
                                    </a>
                                </div>
                                <?php else: ?>
                                <?php foreach ($pointOfSales as $pos): ?>
                                <div class="pos-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($pos['name']); ?></h6>
                                            <small class="text-muted"><?php echo htmlspecialchars($pos['type']); ?></small>
                                        </div>
                                        <div>
                                            <?php if ($pos['is_active']): ?>
                                            <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                            <span class="badge bg-danger">غير نشط</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- إجراءات خطيرة -->
                        <div class="card mt-3 border-danger">
                            <div class="card-header bg-danger text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    إجراءات خطيرة
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">هذه الإجراءات لا يمكن التراجع عنها</p>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-outline-danger" onclick="confirmDelete()">
                                        <i class="fas fa-trash me-2"></i>
                                        حذف الفرع نهائياً
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>

            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // إعادة تعيين النموذج
        function resetForm() {
            if (confirm('هل أنت متأكد من إلغاء جميع التغييرات؟')) {
                location.reload();
            }
        }

        // تأكيد الحذف
        function confirmDelete() {
            if (confirm('هل أنت متأكد من حذف هذا الفرع نهائياً؟\n\nسيتم حذف جميع البيانات المرتبطة بالفرع بما في ذلك نقاط البيع والمعاملات.\n\nهذا الإجراء لا يمكن التراجع عنه!')) {
                if (confirm('تأكيد نهائي: هل أنت متأكد 100% من حذف الفرع؟')) {
                    window.location.href = 'delete_branch.php?id=<?php echo $branchId; ?>';
                }
            }
        }

        // التحقق من صحة النموذج
        (function() {
            'use strict';

            const form = document.getElementById('branchForm');

            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }

                form.classList.add('was-validated');
            }, false);
        })();

        // تنسيق رقم الهاتف
        document.getElementById('phone').addEventListener('input', function() {
            let value = this.value.replace(/[^\d+\-\s]/g, '');
            this.value = value;
        });

        // إضافة تأثيرات بصرية
        document.querySelectorAll('.form-control:not(.readonly-field), .form-select').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
                this.parentElement.style.transition = 'transform 0.2s ease';
            });

            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>
