<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

checkSession();
$user = getUserById($_SESSION['user_id']);

// جلب إحصائيات شاملة
try {
    $stats = [
        'total_branches' => fetchOne("SELECT COUNT(*) as count FROM branches")['count'] ?? 0,
        'active_branches' => fetchOne("SELECT COUNT(*) as count FROM branches WHERE is_active = 1")['count'] ?? 0,
        'total_pos' => fetchOne("SELECT COUNT(*) as count FROM point_of_sales")['count'] ?? 0,
        'active_pos' => fetchOne("SELECT COUNT(*) as count FROM point_of_sales WHERE is_active = 1")['count'] ?? 0,
        'total_users' => fetchOne("SELECT COUNT(*) as count FROM users")['count'] ?? 0,
        'total_companies' => fetchOne("SELECT COUNT(*) as count FROM companies WHERE is_active = 1")['count'] ?? 0
    ];
    
    // آخر الفروع المضافة
    $recentBranches = fetchAll("SELECT * FROM branches ORDER BY created_at DESC LIMIT 5");
    
} catch (Exception $e) {
    $stats = array_fill_keys(['total_branches', 'active_branches', 'total_pos', 'active_pos', 'total_users', 'total_companies'], 0);
    $recentBranches = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الفروع المتكامل - نظام المحاسبة المالية</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body { 
            font-family: 'Cairo', sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        /* تخطيط القائمة على اليمين */
        @media (min-width: 768px) {
            .main-content-with-right-sidebar {
                margin-right: 300px;
                padding-right: 20px;
                padding-left: 20px;
            }
        }
        
        @media (max-width: 767.98px) {
            .main-content-with-right-sidebar {
                margin-right: 0;
                padding-right: 15px;
                padding-left: 15px;
            }
        }
        
        .hero-section {
            background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.9));
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: none;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 30px;
            color: white;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .demo-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .success-indicator {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 15px 25px;
            border-radius: 50px;
            display: inline-block;
            margin: 5px;
            font-weight: bold;
        }
        
        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }
        
        .action-btn {
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: bold;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        .recent-branches {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .branch-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>
    
    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Main Content Area -->
            <main class="col-12 px-md-4 main-content-with-right-sidebar">
                <!-- Hero Section -->
                <div class="hero-section">
                    <h1 class="display-4 mb-4">
                        <i class="fas fa-store me-3 text-primary"></i>
                        نظام إدارة الفروع المتكامل
                    </h1>
                    <p class="lead mb-4">
                        نظام شامل ومتطور لإدارة الفروع مع توليد أكواد تلقائية وحماية متقدمة
                    </p>
                    <div class="success-indicator pulse">
                        <i class="fas fa-check-circle me-2"></i>
                        جاهز للاستخدام بجميع المميزات!
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number text-primary"><?php echo $stats['total_branches']; ?></div>
                        <div class="text-muted">إجمالي الفروع</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number text-success"><?php echo $stats['active_branches']; ?></div>
                        <div class="text-muted">الفروع النشطة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number text-info"><?php echo $stats['total_pos']; ?></div>
                        <div class="text-muted">نقاط البيع</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number text-warning"><?php echo $stats['total_users']; ?></div>
                        <div class="text-muted">المستخدمين</div>
                    </div>
                </div>

                <!-- المميزات الرئيسية -->
                <div class="demo-section">
                    <h2 class="text-center mb-4">
                        <i class="fas fa-star text-warning me-2"></i>
                        المميزات المتكاملة
                    </h2>
                    
                    <div class="feature-grid">
                        <div class="feature-card">
                            <div class="feature-icon bg-primary">
                                <i class="fas fa-magic"></i>
                            </div>
                            <h4>كود تلقائي ذكي</h4>
                            <p>توليد أكواد فريدة تلقائياً من أسماء الفروع مع دعم العربية والإنجليزية</p>
                            <a href="test_auto_code.php" class="btn btn-outline-primary">
                                <i class="fas fa-test-tube me-2"></i>
                                اختبر الآن
                            </a>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon bg-success">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <h4>حماية متقدمة</h4>
                            <p>الأكواد محمية من التعديل مع نظام صلاحيات متقدم وتسجيل جميع الأنشطة</p>
                            <a href="edit_branch.php?id=1" class="btn btn-outline-success">
                                <i class="fas fa-lock me-2"></i>
                                شاهد الحماية
                            </a>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon bg-info">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <h4>إدارة شاملة</h4>
                            <p>إضافة، تعديل، عرض، وحذف آمن مع خيارات الحذف الناعم والنهائي</p>
                            <a href="branches.php" class="btn btn-outline-info">
                                <i class="fas fa-list me-2"></i>
                                إدارة الفروع
                            </a>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon bg-warning">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <h4>تقارير وإحصائيات</h4>
                            <p>عرض تفصيلي لكل فرع مع الإحصائيات والمعاملات ونقاط البيع</p>
                            <a href="view_branch.php?id=1" class="btn btn-outline-warning">
                                <i class="fas fa-eye me-2"></i>
                                عرض التفاصيل
                            </a>
                        </div>
                    </div>
                </div>

                <!-- العمليات المتاحة -->
                <div class="demo-section">
                    <h2 class="text-center mb-4">
                        <i class="fas fa-tasks text-success me-2"></i>
                        العمليات المتاحة
                    </h2>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5>✅ العمليات الأساسية:</h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">
                                    <i class="fas fa-plus text-primary me-2"></i>
                                    إضافة فرع جديد مع كود تلقائي
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-edit text-success me-2"></i>
                                    تعديل بيانات الفرع (عدا الكود)
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-eye text-info me-2"></i>
                                    عرض تفاصيل شاملة لكل فرع
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-trash text-danger me-2"></i>
                                    حذف آمن (ناعم ونهائي)
                                </li>
                            </ul>
                        </div>
                        
                        <div class="col-md-6">
                            <h5>🔒 الأمان والحماية:</h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">
                                    <i class="fas fa-lock text-warning me-2"></i>
                                    الأكواد محمية من التعديل
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-user-shield text-primary me-2"></i>
                                    نظام صلاحيات متقدم
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-history text-info me-2"></i>
                                    تسجيل جميع الأنشطة
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-database text-success me-2"></i>
                                    فحص البيانات المرتبطة قبل الحذف
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- آخر الفروع المضافة -->
                <?php if (!empty($recentBranches)): ?>
                <div class="demo-section">
                    <h2 class="text-center mb-4">
                        <i class="fas fa-clock text-info me-2"></i>
                        آخر الفروع المضافة
                    </h2>
                    
                    <div class="recent-branches">
                        <?php foreach ($recentBranches as $branch): ?>
                        <div class="branch-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">
                                        <i class="fas fa-store me-2"></i>
                                        <?php echo htmlspecialchars($branch['name']); ?>
                                    </h6>
                                    <small class="text-muted">
                                        الكود: <code><?php echo htmlspecialchars($branch['code']); ?></code> |
                                        تاريخ الإضافة: <?php echo date('Y-m-d H:i', strtotime($branch['created_at'])); ?>
                                    </small>
                                </div>
                                <div>
                                    <?php if ($branch['is_active']): ?>
                                    <span class="badge bg-success">نشط</span>
                                    <?php else: ?>
                                    <span class="badge bg-secondary">غير نشط</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- معلومات المستخدم -->
                <div class="demo-section">
                    <h2 class="text-center mb-4">
                        <i class="fas fa-user text-primary me-2"></i>
                        معلومات المستخدم الحالي
                    </h2>
                    
                    <div class="row justify-content-center">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-user-circle fa-4x text-primary"></i>
                                    </div>
                                    <h5><?php echo htmlspecialchars($user['full_name']); ?></h5>
                                    <p class="text-muted"><?php echo htmlspecialchars($user['user_type']); ?></p>
                                    
                                    <div class="mb-3">
                                        <?php if (isAdmin($_SESSION['user_id'])): ?>
                                        <span class="badge bg-danger fs-6">مدير النظام</span>
                                        <p class="mt-2 mb-0">
                                            <small>لديك صلاحية كاملة لإدارة جميع الفروع</small>
                                        </p>
                                        <?php elseif (isMainAccountant($_SESSION['user_id'])): ?>
                                        <span class="badge bg-warning fs-6">محاسب رئيسي</span>
                                        <p class="mt-2 mb-0">
                                            <small>يمكنك عرض جميع البيانات</small>
                                        </p>
                                        <?php else: ?>
                                        <span class="badge bg-info fs-6">مستخدم عادي</span>
                                        <p class="mt-2 mb-0">
                                            <small>يمكنك عرض فروعك فقط</small>
                                        </p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار العمل -->
                <div class="action-buttons">
                    <a href="add_branch.php" class="action-btn btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة فرع جديد
                    </a>
                    <a href="branches.php" class="action-btn btn btn-success">
                        <i class="fas fa-list me-2"></i>
                        قائمة الفروع
                    </a>
                    <a href="test_auto_code.php" class="action-btn btn btn-warning">
                        <i class="fas fa-magic me-2"></i>
                        اختبار الكود التلقائي
                    </a>
                    <a href="test_branch_management.php" class="action-btn btn btn-info">
                        <i class="fas fa-test-tube me-2"></i>
                        اختبارات شاملة
                    </a>
                    <a href="index.php" class="action-btn btn btn-secondary">
                        <i class="fas fa-home me-2"></i>
                        الصفحة الرئيسية
                    </a>
                </div>
            </main>
            
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تأثيرات بصرية عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير على البطاقات
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });
            
            // تأثير على الإحصائيات
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach((card, index) => {
                setTimeout(() => {
                    const number = card.querySelector('.stat-number');
                    const finalValue = parseInt(number.textContent);
                    let currentValue = 0;
                    
                    const increment = finalValue / 50;
                    const timer = setInterval(() => {
                        currentValue += increment;
                        if (currentValue >= finalValue) {
                            currentValue = finalValue;
                            clearInterval(timer);
                        }
                        number.textContent = Math.floor(currentValue);
                    }, 30);
                }, index * 100);
            });
            
            // تأثير على الأزرار
            document.querySelectorAll('.action-btn').forEach(btn => {
                btn.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px) scale(1.05)';
                });
                
                btn.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
        
        // رسائل ترحيب
        setTimeout(() => {
            if (<?php echo $stats['total_branches']; ?> === 0) {
                if (confirm('مرحباً بك في نظام إدارة الفروع!\n\nلا توجد فروع في النظام حالياً.\nهل تريد إضافة فرع جديد الآن؟')) {
                    window.location.href = 'add_branch.php';
                }
            }
        }, 2000);
    </script>
</body>
</html>
