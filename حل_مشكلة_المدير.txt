========================================
    حل مشكلة "لا تعمل هذه الصفحة"
========================================

🔍 المشكلة:
عند تسجيل الدخول بصلاحية المدير، تظهر رسالة "لا تعمل هذه الصفحة"

🎯 السبب:
قاعدة البيانات غير مثبتة أو الجداول غير موجودة

✅ الحل السريع:

1. افتح المتصفح على:
   http://localhost/MD1/debug.php

2. تحقق من حالة النظام:
   - هل قاعدة البيانات متصلة؟
   - هل الجداول موجودة؟
   - هل المستخدمين موجودين؟

3. إذا كانت قاعدة البيانات غير مثبتة:
   http://localhost/MD1/install.php

4. اتبع خطوات التثبيت:
   - خادم قاعدة البيانات: localhost
   - اسم قاعدة البيانات: schema
   - اسم المستخدم: md
   - كلمة المرور: mm$$12345dd

5. بعد التثبيت، سجل الدخول:
   http://localhost/MD1/login.php

   المدير:
   اسم المستخدم: admin
   كلمة المرور: password

========================================

🔧 خطوات التشخيص:

1. تحقق من XAMPP:
   ✓ Apache يعمل
   ✓ MySQL يعمل

2. تحقق من الملفات:
   http://localhost/MD1/check.php

3. تشخيص متقدم:
   http://localhost/MD1/debug.php

4. اختبار كلمة المرور:
   http://localhost/MD1/test_password.php

========================================

🚨 إذا استمرت المشكلة:

1. احذف ملف config/database.php
2. أعد تشغيل معالج التثبيت
3. تأكد من أن MySQL يعمل في XAMPP
4. تحقق من أن المجلد في المكان الصحيح:
   C:\xampp\htdocs\MD1\

========================================

📞 للمساعدة الإضافية:
- راجع ملف debug.php للتشخيص الشامل
- تحقق من ملف error.log في XAMPP
- تأكد من إصدار PHP (يجب أن يكون 7.4+)

========================================
