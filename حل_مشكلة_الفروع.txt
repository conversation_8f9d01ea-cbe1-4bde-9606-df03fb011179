========================================
    حل مشكلة عدم ظهور صفحة الفروع ونقاط البيع
========================================

🔍 المشكلة:
عند النقر على "الفروع ونقاط البيع" لا تظهر الصفحة أو تظهر خطأ

🎯 السبب المحتمل:
1. ملفات الفروع غير موجودة
2. قاعدة البيانات غير مثبتة
3. البيانات التجريبية غير موجودة

✅ الحل السريع:

1. افتح أداة الإصلاح:
   http://localhost/MD1/fix_branches.php

2. ستقوم الأداة بـ:
   ✓ فحص وجود الجداول المطلوبة
   ✓ فحص وجود ملفات الفروع
   ✓ إضافة البيانات التجريبية إذا لزم الأمر
   ✓ اختبار الروابط

3. بعد الإصلاح، جرب الروابط:
   - قائمة الفروع: http://localhost/MD1/modules/branches/list.php
   - قائمة نقاط البيع: http://localhost/MD1/modules/pos/list.php

========================================

📁 الملفات التي تم إنشاؤها:

✅ modules/branches/list.php - قائمة الفروع
✅ modules/branches/add.php - إضافة فرع جديد  
✅ modules/branches/view.php - عرض تفاصيل الفرع
✅ modules/pos/list.php - قائمة نقاط البيع
✅ modules/pos/add.php - إضافة نقطة بيع جديدة

========================================

🏪 البيانات التجريبية المضافة:

الفروع:
- فرع الزرقاء (ZRQ001)
- فرع ماركا (MRK001)  
- فرع قفقفا (QFQ001)

نقاط البيع:
- سوبر ماركت الزرقاء
- كافتيريا الزرقاء
- محمص الزرقاء
- حلويات الزرقاء
- مخبز الزرقاء
- سوبر ماركت ماركا
- كافتيريا ماركا
- سوبر ماركت قفقفا

========================================

🔧 إذا استمرت المشكلة:

1. تأكد من تسجيل الدخول بصلاحية مدير أو محاسب رئيسي
2. تحقق من أن قاعدة البيانات مثبتة بشكل صحيح
3. راجع ملف debug.php للتشخيص الشامل
4. تأكد من أن Apache و MySQL يعملان في XAMPP

========================================

🎯 الوظائف المتاحة بعد الإصلاح:

للمدير:
- عرض جميع الفروع ونقاط البيع
- إضافة/تعديل/حذف الفروع
- إضافة/تعديل/حذف نقاط البيع
- عرض إحصائيات الفروع

للمحاسب الرئيسي:
- عرض جميع الفروع ونقاط البيع
- عرض إحصائيات الفروع

للمحاسب الفرعي:
- عرض فرعه ونقاط البيع التابعة له فقط

========================================

📞 للمساعدة الإضافية:
- استخدم أداة التشخيص: debug.php
- راجع ملف error.log في XAMPP
- تأكد من الصلاحيات المناسبة

========================================
