<?php
require_once 'config/database.php';

// وظائف المصادقة والأمان
function authenticateUser($username, $password) {
    $sql = "SELECT * FROM users WHERE username = ? AND is_active = 1";
    $user = fetchOne($sql, [$username]);
    
    if ($user && password_verify($password, $user['password'])) {
        // تحديث آخر تسجيل دخول
        $updateSql = "UPDATE users SET last_login = NOW(), login_attempts = 0 WHERE id = ?";
        executeQuery($updateSql, [$user['id']]);
        
        return $user;
    }
    
    // زيادة عدد محاولات تسجيل الدخول الفاشلة
    if ($user) {
        $attempts = $user['login_attempts'] + 1;
        $lockUntil = null;
        
        if ($attempts >= MAX_LOGIN_ATTEMPTS) {
            $lockUntil = date('Y-m-d H:i:s', time() + LOGIN_LOCKOUT_TIME);
        }
        
        $updateSql = "UPDATE users SET login_attempts = ?, locked_until = ? WHERE id = ?";
        executeQuery($updateSql, [$attempts, $lockUntil, $user['id']]);
    }
    
    return false;
}

function getUserById($userId) {
    $sql = "SELECT u.*, b.name as branch_name, p.name as pos_name 
            FROM users u 
            LEFT JOIN branches b ON u.branch_id = b.id 
            LEFT JOIN point_of_sales p ON u.pos_id = p.id 
            WHERE u.id = ?";
    return fetchOne($sql, [$userId]);
}

function hasPermission($userId, $module, $permission) {
    $sql = "SELECT granted FROM user_permissions 
            WHERE user_id = ? AND module = ? AND permission_type = ?";
    $result = fetchOne($sql, [$userId, $module, $permission]);
    
    return $result ? $result['granted'] : false;
}

function isAdmin($userId) {
    $user = getUserById($userId);
    return $user && $user['user_type'] === USER_TYPE_ADMIN;
}

function isMainAccountant($userId) {
    $user = getUserById($userId);
    return $user && $user['user_type'] === USER_TYPE_MAIN_ACCOUNTANT;
}

// وظائف الفروع ونقاط البيع
function getAllBranches() {
    $sql = "SELECT * FROM branches WHERE is_active = 1 ORDER BY name";
    return fetchAll($sql);
}

function getBranchById($branchId) {
    $sql = "SELECT * FROM branches WHERE id = ?";
    return fetchOne($sql, [$branchId]);
}

function getPointOfSalesByBranch($branchId) {
    $sql = "SELECT * FROM point_of_sales WHERE branch_id = ? AND is_active = 1 ORDER BY name";
    return fetchAll($sql, [$branchId]);
}

function getAllPointOfSales() {
    $sql = "SELECT p.*, b.name as branch_name 
            FROM point_of_sales p 
            JOIN branches b ON p.branch_id = b.id 
            WHERE p.is_active = 1 
            ORDER BY b.name, p.name";
    return fetchAll($sql);
}

// وظائف الإحصائيات
function getTodayStatistics() {
    $today = date('Y-m-d');
    
    // إجمالي المبيعات اليوم
    $salesSql = "SELECT COALESCE(SUM(total_amount), 0) as total_sales 
                 FROM invoices 
                 WHERE invoice_type = 'sale' 
                 AND invoice_date = ? 
                 AND status != 'cancelled'";
    $salesResult = fetchOne($salesSql, [$today]);
    
    // إجمالي المشتريات اليوم
    $purchasesSql = "SELECT COALESCE(SUM(total_amount), 0) as total_purchases 
                     FROM invoices 
                     WHERE invoice_type = 'purchase' 
                     AND invoice_date = ? 
                     AND status != 'cancelled'";
    $purchasesResult = fetchOne($purchasesSql, [$today]);
    
    // عدد الفواتير اليوم
    $invoicesSql = "SELECT COUNT(*) as total_invoices 
                    FROM invoices 
                    WHERE invoice_date = ? 
                    AND status != 'cancelled'";
    $invoicesResult = fetchOne($invoicesSql, [$today]);
    
    return [
        'total_sales' => $salesResult['total_sales'],
        'total_purchases' => $purchasesResult['total_purchases'],
        'total_invoices' => $invoicesResult['total_invoices'],
        'net_profit' => $salesResult['total_sales'] - $purchasesResult['total_purchases']
    ];
}

function getMonthlySalesData() {
    $sql = "SELECT MONTH(invoice_date) as month, SUM(total_amount) as total 
            FROM invoices 
            WHERE invoice_type = 'sale' 
            AND YEAR(invoice_date) = YEAR(CURDATE()) 
            AND status != 'cancelled'
            GROUP BY MONTH(invoice_date) 
            ORDER BY MONTH(invoice_date)";
    
    $results = fetchAll($sql);
    $data = array_fill(0, 12, 0);
    
    foreach ($results as $result) {
        $data[$result['month'] - 1] = (float)$result['total'];
    }
    
    return $data;
}

function getBranchPerformanceData() {
    $sql = "SELECT b.name, COALESCE(SUM(i.total_amount), 0) as total 
            FROM branches b 
            LEFT JOIN invoices i ON b.id = i.branch_id 
            AND i.invoice_type = 'sale' 
            AND i.status != 'cancelled'
            AND MONTH(i.invoice_date) = MONTH(CURDATE())
            WHERE b.is_active = 1
            GROUP BY b.id, b.name 
            ORDER BY total DESC";
    
    $results = fetchAll($sql);
    return array_column($results, 'total');
}

// وظائف الأنشطة والسجلات
function logActivity($userId, $action, $description, $tableName = null, $recordId = null) {
    $sql = "INSERT INTO activity_logs (user_id, action, description, table_name, record_id, ip_address, user_agent) 
            VALUES (?, ?, ?, ?, ?, ?, ?)";
    
    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
    
    executeQuery($sql, [$userId, $action, $description, $tableName, $recordId, $ipAddress, $userAgent]);
}

function getRecentActivities($limit = 10) {
    $sql = "SELECT a.*, u.full_name as user_name 
            FROM activity_logs a 
            JOIN users u ON a.user_id = u.id 
            ORDER BY a.created_at DESC 
            LIMIT ?";
    
    return fetchAll($sql, [$limit]);
}

// وظائف المخزون
function getLowStockItems($limit = 10) {
    $sql = "SELECT p.name as product_name, i.current_stock, p.min_stock_level, b.name as branch_name 
            FROM inventory i 
            JOIN products p ON i.product_id = p.id 
            JOIN branches b ON i.branch_id = b.id 
            WHERE i.current_stock <= p.min_stock_level 
            AND p.is_active = 1 
            ORDER BY i.current_stock ASC 
            LIMIT ?";
    
    return fetchAll($sql, [$limit]);
}

function updateInventory($productId, $branchId, $posId, $quantity, $operation = 'add') {
    // التحقق من وجود المنتج في المخزون
    $sql = "SELECT * FROM inventory 
            WHERE product_id = ? AND branch_id = ? AND pos_id = ?";
    $inventory = fetchOne($sql, [$productId, $branchId, $posId]);
    
    if ($inventory) {
        // تحديث المخزون الموجود
        $newStock = $operation === 'add' ? 
            $inventory['current_stock'] + $quantity : 
            $inventory['current_stock'] - $quantity;
        
        $updateSql = "UPDATE inventory SET current_stock = ? WHERE id = ?";
        executeQuery($updateSql, [$newStock, $inventory['id']]);
    } else {
        // إضافة منتج جديد للمخزون
        $initialStock = $operation === 'add' ? $quantity : 0;
        $insertSql = "INSERT INTO inventory (product_id, branch_id, pos_id, current_stock) 
                      VALUES (?, ?, ?, ?)";
        executeQuery($insertSql, [$productId, $branchId, $posId, $initialStock]);
    }
}

// وظائف الفواتير
function generateInvoiceNumber($type) {
    $prefix = $type === 'sale' ? 'SAL' : 'PUR';
    $year = date('Y');
    $month = date('m');
    
    // البحث عن آخر رقم فاتورة
    $sql = "SELECT MAX(CAST(SUBSTRING(invoice_number, -6) AS UNSIGNED)) as last_number 
            FROM invoices 
            WHERE invoice_number LIKE ? 
            AND YEAR(created_at) = ?";
    
    $pattern = $prefix . $year . $month . '%';
    $result = fetchOne($sql, [$pattern, $year]);
    
    $nextNumber = ($result['last_number'] ?? 0) + 1;
    
    return $prefix . $year . $month . str_pad($nextNumber, 6, '0', STR_PAD_LEFT);
}

function createInvoice($data) {
    try {
        beginTransaction();
        
        // إنشاء الفاتورة
        $invoiceNumber = generateInvoiceNumber($data['invoice_type']);
        
        $sql = "INSERT INTO invoices (invoice_number, invoice_type, branch_id, pos_id, customer_id, supplier_id, 
                user_id, invoice_date, due_date, subtotal, tax_amount, discount_amount, total_amount, 
                paid_amount, remaining_amount, status, notes) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        executeQuery($sql, [
            $invoiceNumber,
            $data['invoice_type'],
            $data['branch_id'],
            $data['pos_id'] ?? null,
            $data['customer_id'] ?? null,
            $data['supplier_id'] ?? null,
            $data['user_id'],
            $data['invoice_date'],
            $data['due_date'] ?? null,
            $data['subtotal'],
            $data['tax_amount'],
            $data['discount_amount'],
            $data['total_amount'],
            $data['paid_amount'] ?? 0,
            $data['total_amount'] - ($data['paid_amount'] ?? 0),
            $data['status'] ?? 'draft',
            $data['notes'] ?? null
        ]);
        
        $invoiceId = getLastInsertId();
        
        // إضافة تفاصيل الفاتورة
        foreach ($data['items'] as $item) {
            $itemSql = "INSERT INTO invoice_items (invoice_id, product_id, quantity, unit_price, 
                        discount_percentage, discount_amount, tax_percentage, tax_amount, total_amount) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            executeQuery($itemSql, [
                $invoiceId,
                $item['product_id'],
                $item['quantity'],
                $item['unit_price'],
                $item['discount_percentage'] ?? 0,
                $item['discount_amount'] ?? 0,
                $item['tax_percentage'] ?? 0,
                $item['tax_amount'] ?? 0,
                $item['total_amount']
            ]);
            
            // تحديث المخزون
            if ($data['invoice_type'] === 'sale') {
                updateInventory($item['product_id'], $data['branch_id'], $data['pos_id'], $item['quantity'], 'subtract');
            } elseif ($data['invoice_type'] === 'purchase') {
                updateInventory($item['product_id'], $data['branch_id'], $data['pos_id'], $item['quantity'], 'add');
            }
        }
        
        commit();
        
        // تسجيل النشاط
        logActivity($data['user_id'], 'إنشاء فاتورة', "تم إنشاء فاتورة رقم: $invoiceNumber", 'invoices', $invoiceId);
        
        return $invoiceId;
        
    } catch (Exception $e) {
        rollback();
        throw $e;
    }
}

// وظائف التقارير
function getSalesReport($startDate, $endDate, $branchId = null, $posId = null) {
    $sql = "SELECT i.*, c.name as customer_name, b.name as branch_name, p.name as pos_name, u.full_name as user_name 
            FROM invoices i 
            LEFT JOIN customers c ON i.customer_id = c.id 
            JOIN branches b ON i.branch_id = b.id 
            LEFT JOIN point_of_sales p ON i.pos_id = p.id 
            JOIN users u ON i.user_id = u.id 
            WHERE i.invoice_type = 'sale' 
            AND i.invoice_date BETWEEN ? AND ? 
            AND i.status != 'cancelled'";
    
    $params = [$startDate, $endDate];
    
    if ($branchId) {
        $sql .= " AND i.branch_id = ?";
        $params[] = $branchId;
    }
    
    if ($posId) {
        $sql .= " AND i.pos_id = ?";
        $params[] = $posId;
    }
    
    $sql .= " ORDER BY i.invoice_date DESC, i.created_at DESC";
    
    return fetchAll($sql, $params);
}

function getPurchaseReport($startDate, $endDate, $branchId = null) {
    $sql = "SELECT i.*, s.name as supplier_name, b.name as branch_name, u.full_name as user_name 
            FROM invoices i 
            LEFT JOIN suppliers s ON i.supplier_id = s.id 
            JOIN branches b ON i.branch_id = b.id 
            JOIN users u ON i.user_id = u.id 
            WHERE i.invoice_type = 'purchase' 
            AND i.invoice_date BETWEEN ? AND ? 
            AND i.status != 'cancelled'";
    
    $params = [$startDate, $endDate];
    
    if ($branchId) {
        $sql .= " AND i.branch_id = ?";
        $params[] = $branchId;
    }
    
    $sql .= " ORDER BY i.invoice_date DESC, i.created_at DESC";
    
    return fetchAll($sql, $params);
}

// وظائف مساعدة
function formatCurrency($amount) {
    return number_format($amount, 2) . ' ' . CURRENCY_SYMBOL;
}

function formatDate($date) {
    return date('Y-m-d', strtotime($date));
}

function formatDateTime($datetime) {
    return date('Y-m-d H:i:s', strtotime($datetime));
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'منذ لحظات';
    if ($time < 3600) return 'منذ ' . floor($time/60) . ' دقيقة';
    if ($time < 86400) return 'منذ ' . floor($time/3600) . ' ساعة';
    if ($time < 2592000) return 'منذ ' . floor($time/86400) . ' يوم';
    if ($time < 31104000) return 'منذ ' . floor($time/2592000) . ' شهر';
    
    return 'منذ ' . floor($time/31104000) . ' سنة';
}

function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

function generateRandomPassword($length = 8) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $password = '';
    
    for ($i = 0; $i < $length; $i++) {
        $password .= $characters[rand(0, strlen($characters) - 1)];
    }
    
    return $password;
}

// وظائف النسخ الاحتياطي
function createBackup($description = null) {
    $filename = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
    $filepath = BACKUP_PATH . $filename;
    
    // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
    if (!is_dir(BACKUP_PATH)) {
        mkdir(BACKUP_PATH, 0755, true);
    }
    
    // تنفيذ النسخ الاحتياطي
    $command = "mysqldump --host=" . DB_HOST . " --user=" . DB_USER . " --password=" . DB_PASS . " " . DB_NAME . " > " . $filepath;
    exec($command, $output, $returnVar);
    
    if ($returnVar === 0 && file_exists($filepath)) {
        $fileSize = filesize($filepath);
        
        // حفظ معلومات النسخة الاحتياطية في قاعدة البيانات
        $sql = "INSERT INTO backups (filename, file_size, backup_type, description, user_id) 
                VALUES (?, ?, 'manual', ?, ?)";
        executeQuery($sql, [$filename, $fileSize, $description, $_SESSION['user_id'] ?? null]);
        
        return $filename;
    }
    
    return false;
}

// وظائف الإعدادات
function getSetting($key, $default = null) {
    $sql = "SELECT setting_value FROM settings WHERE setting_key = ?";
    $result = fetchOne($sql, [$key]);
    
    return $result ? $result['setting_value'] : $default;
}

function setSetting($key, $value, $description = null) {
    $sql = "INSERT INTO settings (setting_key, setting_value, description, updated_by) 
            VALUES (?, ?, ?, ?) 
            ON DUPLICATE KEY UPDATE 
            setting_value = VALUES(setting_value), 
            description = VALUES(description), 
            updated_by = VALUES(updated_by)";
    
    executeQuery($sql, [$key, $value, $description, $_SESSION['user_id'] ?? null]);
}

// التحقق من الجلسة
function checkSession() {
    if (!isset($_SESSION['user_id'])) {
        header('Location: login.php');
        exit();
    }
    
    // التحقق من انتهاء الجلسة
    if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT)) {
        session_destroy();
        header('Location: login.php?timeout=1');
        exit();
    }
    
    $_SESSION['last_activity'] = time();
}

// تسجيل الخروج
function logout() {
    if (isset($_SESSION['user_id'])) {
        logActivity($_SESSION['user_id'], 'تسجيل خروج', 'تم تسجيل الخروج بنجاح');
    }
    
    session_destroy();
    header('Location: login.php');
    exit();
}
?>
