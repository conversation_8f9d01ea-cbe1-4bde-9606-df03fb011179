# نظام المحاسبة المالية - إعدادات Apache

# تفعيل إعادة الكتابة
RewriteEngine On

# منع الوصول للملفات الحساسة
<Files "config/database.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "includes/functions.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files ".htaccess">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.log">
    Order Allow,Deny
    Deny from all
</Files>

# منع الوصول للمجلدات الحساسة
<Directory "backups">
    Order Allow,Deny
    Deny from all
</Directory>

<Directory "logs">
    Order Allow,Deny
    Deny from all
</Directory>

# إعدادات الأمان
<IfModule mod_headers.c>
    # منع تضمين الموقع في إطارات خارجية
    Header always append X-Frame-Options SAMEORIGIN
    
    # منع تخمين نوع المحتوى
    Header set X-Content-Type-Options nosniff
    
    # تفعيل حماية XSS
    Header set X-XSS-Protection "1; mode=block"
    
    # إخفاء معلومات الخادم
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تخزين مؤقت للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# إعادة توجيه الصفحات
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# إعادة توجيه للصفحة الرئيسية إذا لم يتم العثور على الملف
RewriteRule ^$ index.php [L]

# منع الوصول للملفات المخفية
<FilesMatch "^\.">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# إعدادات PHP
<IfModule mod_php7.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_vars 3000
    php_value memory_limit 256M
    php_value session.gc_maxlifetime 3600
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 0
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log logs/error.log
</IfModule>

# منع تنفيذ ملفات PHP في مجلد الرفع
<Directory "uploads">
    <FilesMatch "\.php$">
        Order Allow,Deny
        Deny from all
    </FilesMatch>
</Directory>

# صفحات الخطأ المخصصة
ErrorDocument 404 /MD1/error.php?code=404
ErrorDocument 403 /MD1/error.php?code=403
ErrorDocument 500 /MD1/error.php?code=500

# منع الوصول لملفات النسخ الاحتياطية
<FilesMatch "\.(sql|bak|backup)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# حماية من الهجمات الشائعة
<IfModule mod_rewrite.c>
    RewriteCond %{QUERY_STRING} (<|%3C).*script.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} ^.*(\[|\]|\(|\)|<|>|ê|"|;|\?|\*|=$).* [NC,OR]
    RewriteCond %{QUERY_STRING} ^.*("|'|<|>|\|{||).* [NC,OR]
    RewriteCond %{QUERY_STRING} ^.*(%0|%A|%B|%C|%D|%E|%F|127\.0).* [NC,OR]
    RewriteCond %{QUERY_STRING} ^.*(globals|encode|localhost|loopback).* [NC,OR]
    RewriteCond %{QUERY_STRING} ^.*(request|select|insert|union|declare).* [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# تحسين الأداء
<IfModule mod_mime.c>
    AddType text/css .css
    AddType application/x-javascript .js
    AddType text/x-component .htc
    AddType application/x-font-ttf .ttf
    AddType application/x-font-otf .otf
    AddType application/font-woff .woff
    AddType application/font-woff2 .woff2
    AddType image/svg+xml .svg
</IfModule>
