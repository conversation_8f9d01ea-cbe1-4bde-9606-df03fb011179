# نظام المحاسبة المالية - إعدادات Apache المبسطة

# تفعيل إعادة الكتابة
RewriteEngine On

# منع الوصول للملفات الحساسة
<Files "config/database.php">
    Require all denied
</Files>

<Files "includes/functions.php">
    Require all denied
</Files>

<Files "*.log">
    Require all denied
</Files>

<Files "*.sql">
    Require all denied
</Files>

# إعدادات الأمان الأساسية
<IfModule mod_headers.c>
    Header always append X-Frame-Options SAMEORIGIN
    Header set X-Content-Type-Options nosniff
    Header set X-XSS-Protection "1; mode=block"
</IfModule>

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/javascript
</IfModule>

# إعدادات PHP الأساسية
<IfModule mod_php.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value memory_limit 256M
    php_flag display_errors Off
    php_flag log_errors On
</IfModule>

# منع الوصول للملفات المخفية
<FilesMatch "^\.">
    Require all denied
</FilesMatch>

# منع الوصول لملفات النسخ الاحتياطية
<FilesMatch "\.(bak|backup)$">
    Require all denied
</FilesMatch>
