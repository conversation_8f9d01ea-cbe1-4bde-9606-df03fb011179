<?php
echo "<h2>اختبار قاعدة البيانات الجديدة - MD3</h2>";

// بيانات الاتصال الجديدة
$host = "localhost";
$username = "md";
$password = "mm\$\$12345dd";
$database = "md3";

echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin-bottom: 20px;'>";
echo "<h3>📊 بيانات قاعدة البيانات الجديدة:</h3>";
echo "<ul>";
echo "<li><strong>الخادم:</strong> $host</li>";
echo "<li><strong>قاعدة البيانات:</strong> <span style='color: #1976d2; font-weight: bold;'>$database</span></li>";
echo "<li><strong>المستخدم:</strong> $username</li>";
echo "<li><strong>كلمة المرور:</strong> " . str_repeat('*', strlen($password)) . "</li>";
echo "</ul>";
echo "</div>";

// اختبار الاتصال
echo "<h3>🔍 اختبار الاتصال:</h3>";

try {
    // محاولة الاتصال بدون تحديد قاعدة البيانات أولاً
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<div style='color: green;'>✓ تم الاتصال بالخادم بنجاح!</div>";
    
    // فحص وجود قاعدة البيانات
    $stmt = $pdo->query("SHOW DATABASES LIKE '$database'");
    if ($stmt->rowCount() > 0) {
        echo "<div style='color: green;'>✓ قاعدة البيانات '$database' موجودة!</div>";
        
        // الاتصال بقاعدة البيانات
        $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "<div style='color: green;'>✓ تم الاتصال بقاعدة البيانات '$database' بنجاح!</div>";
        
        // فحص الجداول
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (count($tables) > 0) {
            echo "<div style='color: green;'>✓ تم العثور على " . count($tables) . " جدول في قاعدة البيانات</div>";
            
            // فحص الجداول الرئيسية
            $mainTables = ['branches', 'point_of_sales', 'users', 'products'];
            foreach ($mainTables as $table) {
                if (in_array($table, $tables)) {
                    $count = $pdo->query("SELECT COUNT(*) FROM $table")->fetchColumn();
                    echo "<div style='color: green;'>✓ جدول $table: $count سجل</div>";
                } else {
                    echo "<div style='color: red;'>✗ جدول $table غير موجود</div>";
                }
            }
        } else {
            echo "<div style='color: orange;'>⚠ قاعدة البيانات فارغة - لا توجد جداول</div>";
        }
        
    } else {
        echo "<div style='color: orange;'>⚠ قاعدة البيانات '$database' غير موجودة</div>";
        
        // إنشاء قاعدة البيانات
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "<div style='color: green;'>✓ تم إنشاء قاعدة البيانات '$database' بنجاح!</div>";
    }
    
} catch (PDOException $e) {
    echo "<div style='color: red;'>✗ خطأ في الاتصال: " . $e->getMessage() . "</div>";
}

// فحص ملفات الإعدادات
echo "<h3>📁 فحص ملفات الإعدادات:</h3>";

$configFiles = [
    'config/database.php' => 'ملف إعدادات قاعدة البيانات',
    'database/schema.sql' => 'ملف هيكل قاعدة البيانات',
    'install.php' => 'معالج التثبيت'
];

foreach ($configFiles as $file => $description) {
    if (file_exists($file)) {
        echo "<div style='color: green;'>✓ $file موجود - $description</div>";
        
        // فحص محتوى الملف للتأكد من التحديث
        $content = file_get_contents($file);
        if (strpos($content, 'md3') !== false) {
            echo "<div style='color: green;'>  ✓ تم تحديث الملف ليستخدم قاعدة البيانات md3</div>";
        } else {
            echo "<div style='color: orange;'>  ⚠ الملف قد لا يحتوي على اسم قاعدة البيانات الجديد</div>";
        }
    } else {
        echo "<div style='color: red;'>✗ $file غير موجود</div>";
    }
}

echo "<hr>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724;'>";
echo "<h3>✅ ملخص التغيير:</h3>";
echo "<p><strong>تم تغيير اسم قاعدة البيانات من:</strong> <del>schema</del> <strong>إلى:</strong> <span style='color: #1976d2;'>md3</span></p>";
echo "<p><strong>الملفات المحدثة:</strong></p>";
echo "<ul>";
echo "<li>config/database.php</li>";
echo "<li>database/schema.sql</li>";
echo "<li>install.php</li>";
echo "<li>test_connection.php</li>";
echo "<li>جميع ملفات التعليمات</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🚀 الخطوات التالية:</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; color: #856404;'>";
echo "<ol>";
echo "<li><strong>إنشاء قاعدة البيانات:</strong> <a href='install.php'>استخدم معالج التثبيت</a></li>";
echo "<li><strong>أو استيراد يدوي:</strong> استورد ملف database/schema.sql في phpMyAdmin</li>";
echo "<li><strong>تسجيل الدخول:</strong> <a href='login.php'>admin / password</a></li>";
echo "<li><strong>اختبار الفروع:</strong> <a href='branches.php'>صفحة الفروع</a></li>";
echo "</ol>";
echo "</div>";

echo "<div style='text-align: center; margin-top: 20px;'>";
echo "<a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>الصفحة الرئيسية</a>";
echo "<a href='install.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>معالج التثبيت</a>";
echo "<a href='debug.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>تشخيص النظام</a>";
echo "</div>";
?>

<style>
body { 
    font-family: 'Cairo', sans-serif; 
    margin: 20px; 
    direction: rtl;
    line-height: 1.6;
}
h2, h3 { color: #333; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
